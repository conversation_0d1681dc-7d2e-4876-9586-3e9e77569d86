<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Anti-Aging Day Cream - Ingredient Infographic</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="infographic-container">
      <!-- Header -->
      <div class="header-section">
        <div class="product-title">ANTI-AGING DAY CREAM</div>
        <div class="subtitle">ADVANCED HYDRATION & PROTECTION</div>
        <div class="divider-line"></div>
      </div>

      <!-- Hero Ingredient -->
      <div class="hero-ingredient">
        <div class="ingredient-number">N°1</div>
        <div class="ingredient-content">
          <div class="ingredient-name">HYALURONIC ACID COMPLEX</div>
          <div class="ingredient-subtitle">Multi-Molecular Hydration</div>
          <div class="ingredient-benefit">
            Dual-action hydrolyzed + sodium hyaluronate delivers instant
            plumping and long-lasting moisture retention for visibly smoother
            skin.
          </div>
        </div>
        <div class="ingredient-icon">💧</div>
      </div>

      <!-- Supporting Ingredients Grid -->
      <div class="ingredients-grid">
        <div class="ingredient-card">
          <div class="card-number">N°2</div>
          <div class="card-name">ALOE BARBADENSIS JUICE</div>
          <div class="card-subtitle">Soothing Base</div>
          <div class="card-benefit">
            Pure aloe leaf juice provides anti-inflammatory comfort while
            delivering vitamins and minerals for healthy skin renewal.
          </div>
          <div class="card-icon">🌿</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°3</div>
          <div class="card-name">MARINE FERMENT EXTRACT</div>
          <div class="card-subtitle">Elasticity Enhancer</div>
          <div class="card-benefit">
            Alteromonas ferment from deep sea organisms improves skin elasticity
            and firmness while boosting cellular renewal for visibly lifted
            skin.
          </div>
          <div class="card-icon">🌊</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°4</div>
          <div class="card-name">NORDIC BERRY COMPLEX</div>
          <div class="card-subtitle">Antioxidant Shield</div>
          <div class="card-benefit">
            Lingonberry, black currant & elderflower extracts provide powerful
            free radical protection and vitamin C boost.
          </div>
          <div class="card-icon">🫐</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°5</div>
          <div class="card-name">JOJOBA + SHEA COMPLEX</div>
          <div class="card-subtitle">Barrier Protection</div>
          <div class="card-benefit">
            Biomimetic oils strengthen skin barrier while providing all-day
            moisture without heaviness or greasiness.
          </div>
          <div class="card-icon">🛡️</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°6</div>
          <div class="card-name">ECHINACEA EXTRACT</div>
          <div class="card-subtitle">Skin Immunity Booster</div>
          <div class="card-benefit">
            Coneflower extract strengthens skin's natural defense system and
            promotes healthy cellular turnover for radiant complexion.
          </div>
          <div class="card-icon">🌸</div>
        </div>
      </div>

      <!-- Key Benefits Summary -->
      <div class="benefits-section">
        <div class="benefits-title">ANTI-AGING BENEFITS</div>
        <div class="benefits-grid">
          <div class="benefit-item">
            <div class="benefit-icon">✨</div>
            <div class="benefit-text">Instant plumping hydration</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">🛡️</div>
            <div class="benefit-text">Environmental protection</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">🌿</div>
            <div class="benefit-text">Soothing inflammation relief</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">⚡</div>
            <div class="benefit-text">Cellular energy boost</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">💎</div>
            <div class="benefit-text">Luxurious daily texture</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">🌅</div>
            <div class="benefit-text">Perfect morning ritual</div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer-section">
        <div class="brand-name">CATHÁM</div>
        <div class="brand-tagline">Where Science Meets Skin Intelligence</div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Fira Code", monospace;
        background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
        padding: 40px 20px;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .infographic-container {
        max-width: 900px;
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 25px 70px rgba(0, 0, 0, 0.12);
        overflow: hidden;
        position: relative;
      }

      /* Header */
      .header-section {
        background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
        color: #ffffff;
        padding: 50px 40px;
        text-align: center;
      }

      .product-title {
        font-family: "Old Standard TT", serif;
        font-size: 32px;
        font-weight: 700;
        letter-spacing: 3px;
        margin-bottom: 15px;
      }

      .subtitle {
        font-size: 14px;
        letter-spacing: 2px;
        opacity: 0.9;
        margin-bottom: 30px;
      }

      .divider-line {
        width: 60px;
        height: 2px;
        background: #ffffff;
        margin: 0 auto;
      }

      /* Hero Ingredient */
      .hero-ingredient {
        padding: 50px 40px;
        background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
        display: flex;
        align-items: center;
        gap: 30px;
        border-bottom: 1px solid #e1f5fe;
      }

      .ingredient-number {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Old Standard TT", serif;
        font-size: 24px;
        font-weight: 700;
        flex-shrink: 0;
      }

      .ingredient-content {
        flex: 1;
      }

      .ingredient-name {
        font-family: "Old Standard TT", serif;
        font-size: 28px;
        font-weight: 700;
        color: #0d47a1;
        margin-bottom: 8px;
        letter-spacing: 1px;
      }

      .ingredient-subtitle {
        font-size: 14px;
        color: #1976d2;
        font-weight: 600;
        margin-bottom: 15px;
        letter-spacing: 1px;
      }

      .ingredient-benefit {
        font-size: 16px;
        line-height: 1.6;
        color: #333333;
      }

      .ingredient-icon {
        font-size: 40px;
        flex-shrink: 0;
      }

      /* Ingredients Grid */
      .ingredients-grid {
        padding: 50px 40px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
      }

      .ingredient-card {
        background: #ffffff;
        border: 1px solid #e1f5fe;
        border-radius: 15px;
        padding: 25px;
        position: relative;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .ingredient-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(25, 118, 210, 0.15);
      }

      .card-number {
        position: absolute;
        top: -15px;
        left: 20px;
        width: 30px;
        height: 30px;
        background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 700;
      }

      .card-name {
        font-family: "Old Standard TT", serif;
        font-size: 16px;
        font-weight: 700;
        color: #0d47a1;
        margin-bottom: 8px;
        letter-spacing: 0.5px;
      }

      .card-subtitle {
        font-size: 11px;
        color: #1976d2;
        font-weight: 600;
        margin-bottom: 12px;
        letter-spacing: 0.5px;
      }

      .card-benefit {
        font-size: 12px;
        line-height: 1.5;
        color: #333333;
        margin-bottom: 12px;
      }

      .card-icon {
        font-size: 20px;
        text-align: right;
      }

      /* Benefits Section */
      .benefits-section {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        padding: 50px 40px;
      }

      .benefits-title {
        font-family: "Old Standard TT", serif;
        font-size: 24px;
        font-weight: 700;
        text-align: center;
        margin-bottom: 30px;
        letter-spacing: 2px;
        color: #0d47a1;
      }

      .benefits-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
      }

      .benefit-item {
        display: flex;
        align-items: center;
        gap: 12px;
        background: #ffffff;
        padding: 18px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(25, 118, 210, 0.08);
      }

      .benefit-icon {
        font-size: 18px;
        flex-shrink: 0;
      }

      .benefit-text {
        font-size: 13px;
        font-weight: 600;
        color: #333333;
      }

      /* Footer */
      .footer-section {
        background: #0d47a1;
        color: #ffffff;
        padding: 40px;
        text-align: center;
      }

      .brand-name {
        font-family: "Old Standard TT", serif;
        font-size: 28px;
        font-weight: 700;
        letter-spacing: 3px;
        margin-bottom: 10px;
      }

      .brand-tagline {
        font-size: 12px;
        letter-spacing: 1px;
        opacity: 0.8;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .infographic-container {
          margin: 20px;
        }

        .header-section,
        .hero-ingredient,
        .ingredients-grid,
        .benefits-section,
        .footer-section {
          padding: 30px 20px;
        }

        .product-title {
          font-size: 24px;
        }

        .hero-ingredient {
          flex-direction: column;
          text-align: center;
        }

        .ingredients-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .benefits-grid {
          grid-template-columns: repeat(2, 1fr);
        }

        .ingredient-name {
          font-size: 24px;
        }

        .card-name {
          font-size: 14px;
        }
      }

      @media (max-width: 480px) {
        .benefits-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </body>
</html>
