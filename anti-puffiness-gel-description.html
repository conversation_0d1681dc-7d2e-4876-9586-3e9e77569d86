<div class="magazine-layout">
  <!-- Magazine Header -->
  <header class="magazine-masthead">
    <div class="masthead-text">Cathám | Clean Beauty Revolution</div>
    <h1 class="magazine-title">REFRESH & REVIVE</h1>
  </header>
  
  <!-- Main Content -->
  <div class="magazine-content">
    <!-- Product Feature Section -->
    <section class="product-feature">
      <div class="feature-text-full">
        <p class="feature-lead">The Antidote for Puffiness and Tired-Looking Skin</p>
        <p>When fatigue shows, our solution glows.</p>
        <div class="product-intro">
          <h2>WAKE UP YOUR SKIN</h2>
          <p><em>Refresh. Revive. Restore.</em></p>
        </div>
        <p class="product-description">This potent gel concentrate, powered by 1% Caffeine, is your go-to solution for reducing puffiness and reviving tired, dull skin. Its lightweight, oil-free formula works to target under-eye fatigue and restore freshness, leaving the skin feeling deeply hydrated and rejuvenated. <span class="highlight">The ultimate morning ritual for skin that needs to wake up before you do.</span></p>
      </div>
    </section>
    
    <!-- Product Details in 90s Magazine Style -->
    <section class="product-details">
      <div class="details-grid">
        <!-- System Section -->
        <div class="details-column">
          <h3 class="column-heading">THE ANTI-INFLAMMATORY COMPLEX</h3>
          
          <div class="phases-grid">
            <div class="phase" data-phase="1">
              <div class="phase-number">N°1</div>
              <h4>Caffeine</h4>
              <p>Powerful 1% concentration works to reduce puffiness and swelling by constricting blood vessels and stimulating circulation for a more energized appearance.</p>
            </div>
            
            <div class="phase" data-phase="2">
              <div class="phase-number">N°2</div>
              <h4>Botanical Actives</h4>
              <p>Linalyl Acetate and Geranyl Acetate from natural essential oils provide scientifically-proven anti-inflammatory benefits to calm and soothe the skin.</p>
            </div>
            
            <div class="phase" data-phase="3">
              <div class="phase-number">N°3</div>
              <h4>Balancing Agents</h4>
              <p>Citric Acid gently balances skin's pH while Sodium Phytate stabilizes the formula, preventing irritation and supporting the delicate skin barrier.</p>
            </div>
          </div>
        </div>
        
        <!-- Benefits Column -->
        <div class="details-column">
          <h3 class="column-heading">KEY BENEFITS</h3>
          
          <ul class="benefits-list">
            <li><span class="benefit-icon">✦</span> <span class="benefit-text"><span class="benefit-title">Reduces Puffiness & Swelling</span> Targeted action on under-eye bags and facial bloating for a more sculpted appearance.</span></li>
            <li><span class="benefit-icon">✦</span> <span class="benefit-text"><span class="benefit-title">Fights Fatigue</span> Revitalizes tired, dull skin and diminishes the appearance of under-eye circles.</span></li>
            <li><span class="benefit-icon">✦</span> <span class="benefit-text"><span class="benefit-title">Hydrates & Refreshes</span> Oil-free formula delivers deep hydration without heaviness for a fresh, youthful look.</span></li>
            <li><span class="benefit-icon">✦</span> <span class="benefit-text"><span class="benefit-title">Strengthens Skin Barrier</span> Supports and protects delicate skin with gentle, non-irritating ingredients.</span></li>
          </ul>
          
          <div class="usage-info">
            <h4>Application Ritual</h4>
            <p>Apply a small amount to clean skin, focusing on under-eye area and any areas of puffiness. Gently pat until absorbed. Ideal for morning use or whenever skin needs a refresh. Perfect for all skin types, especially dehydrated or puffy skin.</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>

<style>
/* 90s Chic Magazine Style CSS */
@import url('https://fonts.googleapis.com/css2?family=Bodoni+Moda:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,600;1,400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500&display=swap');

body {
  background-color: #f5f5f5;
  margin: 0;
  padding: 0;
}

.magazine-layout {
  max-width: 1000px;
  margin: 0 auto;
  font-family: 'Cormorant Garamond', serif;
  color: #111;
  line-height: 1.5;
  padding: 20px;
  background-color: #fff;
}

/* Magazine Masthead */
.magazine-masthead {
  margin-bottom: 30px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 15px;
}

.masthead-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  letter-spacing: 1px;
  color: #666;
  margin-bottom: 10px;
  font-weight: 400;
}

.magazine-title {
  font-family: 'Bodoni Moda', serif;
  font-size: 3.5rem;
  letter-spacing: 1px;
  line-height: 1;
  margin: 0;
  font-weight: 700;
}

/* Feature Text Styling - Full Width */
.feature-text-full {
  padding-top: 10px;
  max-width: 800px;
  margin: 0 auto;
}

.feature-lead {
  font-size: 1.5rem;
  line-height: 1.4;
  margin-bottom: 20px;
  font-weight: 600;
}

.product-intro {
  margin: 30px 0;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  padding: 20px 0;
}

.product-intro h2 {
  font-family: 'Bodoni Moda', serif;
  font-size: 2rem;
  margin: 0 0 10px 0;
  letter-spacing: 1px;
}

.product-description {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 25px;
}

.highlight {
  font-style: italic;
  font-weight: 600;
}

/* Product Details Section */
.product-details {
  margin: 50px 0;
  border-top: 3px double #ddd;
  padding-top: 40px;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.column-heading {
  font-family: 'Bodoni Moda', serif;
  font-size: 1.2rem;
  letter-spacing: 1px;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #ddd;
}

/* Phases Styling */
.phases-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.phase {
  position: relative;
  padding: 20px 15px 15px;
  border: 1px solid #ddd;
  background-color: #fafafa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.phase:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.phase.active {
  border-color: #000;
}

.phase-number {
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  background: #000;
  padding: 2px 8px;
  font-family: 'Times New Roman', Times, serif;
  letter-spacing: 0.05em;
  text-align: right;
}

.phase h4 {
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.1rem;
  margin: 0 0 10px 0;
  font-weight: 600;
}

/* Benefits List */
.benefits-list {
  list-style: none;
  padding: 0;
  margin: 0 0 30px 0;
}

.benefits-list li {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.benefit-icon {
  margin-right: 10px;
  font-size: 1.1rem;
}

.benefit-title {
  display: block;
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.1rem;
  margin-bottom: 3px;
  font-weight: 600;
}

/* Usage Info */
.usage-info {
  background: #f5f5f5;
  padding: 15px;
  margin-top: 30px;
}

.usage-info h4 {
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.1rem;
  margin: 0 0 10px 0;
  font-weight: 600;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .magazine-title {
    font-size: 2.5rem;
  }
  
  .feature-text-full {
    padding: 0 15px;
  }
}
</style>

<script>
// Add interactivity to the phases
document.addEventListener('DOMContentLoaded', function() {
  const phases = document.querySelectorAll('.phase');
  
  // Add click event to each phase
  phases.forEach(phase => {
    phase.addEventListener('click', function() {
      // Toggle active class on clicked phase
      phases.forEach(p => p.classList.remove('active'));
      this.classList.add('active');
      
      // Get the phase number for potential additional actions
      const phaseNum = this.getAttribute('data-phase');
      console.log(`Phase ${phaseNum} clicked`);
    });
  });
});
</script>