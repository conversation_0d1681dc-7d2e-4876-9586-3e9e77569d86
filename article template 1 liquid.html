<!-- Cathám Magazine-Style Article Template -->
<link
  rel="stylesheet"
  href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
/>

<style>
  /* Cathám Magazine Article Styling */
  .catham-article-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 80px 50px;
    background-color: #ffffff;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 12px;
    line-height: 1.65;
    color: #1a1a1a;
    box-shadow: 0 0 60px rgba(0, 0, 0, 0.08);
    position: relative;
  }

  .catham-article-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 50px;
    right: 50px;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      #2c2c2c 50%,
      transparent 100%
    );
  }

  /* Article Header */
  .catham-article-header {
    text-align: center;
    margin-bottom: 60px;
    padding-bottom: 40px;
    position: relative;
    background-color: transparent;
    color: #1a1a1a;
    padding-top: 0;
  }

  .catham-article-header::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      #1a1a1a 50%,
      transparent 100%
    );
  }

  .catham-header-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    font-family: "Fira Code", monospace;
    font-size: 12px;
    letter-spacing: 2px;
    font-weight: 500;
    color: #666;
  }

  .catham-article-title {
    font-family: "Old Standard TT", serif;
    font-size: 3.2rem;
    font-weight: 700;
    line-height: 0.95;
    margin-bottom: 25px;
    letter-spacing: -0.03em;
    text-transform: uppercase;
    position: relative;
    color: #000000;
  }

  .catham-article-title::before {
    content: "";
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #2c2c2c;
  }

  .catham-article-subtitle {
    font-family: "Old Standard TT", serif;
    font-size: 1.4rem;
    font-style: italic;
    margin-bottom: 40px;
    color: #666;
    letter-spacing: 0.08em;
    text-transform: lowercase;
  }

  .catham-byline {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-family: "Fira Code", monospace;
    font-size: 14px;
    color: #666;
  }

  /* Article Content */
  .catham-article-content {
    padding: 0;
    max-width: 100%;
    margin: 0 auto;
  }

  .catham-article-text {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 12px;
    margin-bottom: 25px;
    line-height: 1.7;
    max-width: 100%;
    color: #333333;
    text-align: left;
  }

  .catham-article-text h2 {
    font-family: "Old Standard TT", serif;
    font-size: 2.4rem;
    font-weight: 700;
    color: #000000;
    margin: 80px 0 40px;
    letter-spacing: -0.02em;
    line-height: 1.1;
    position: relative;
    padding-left: 30px;
  }

  .catham-article-text h2::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #2c2c2c 0%, #1a1a1a 100%);
  }

  .catham-article-text p {
    margin-bottom: 20px;
  }

  .catham-article-text strong {
    font-weight: 600;
    color: #000000;
  }

  .catham-article-text a {
    color: #000000;
    text-decoration: underline;
    font-weight: 600;
    transition: opacity 0.3s ease;
  }

  .catham-article-text a:hover {
    opacity: 0.7;
  }

  /* Article Footer */
  .catham-article-footer {
    margin-top: 80px;
    padding-top: 40px;
    border-top: 2px solid #e8e8e8;
    text-align: center;
  }

  .catham-tags {
    margin-bottom: 30px;
  }

  .tags-label {
    font-family: "Fira Code", monospace;
    font-size: 11px;
    color: #666;
    margin-right: 15px;
    letter-spacing: 1px;
    text-transform: uppercase;
  }

  .catham-tag {
    display: inline-block;
    padding: 8px 15px;
    margin: 0 5px 10px 0;
    background-color: #f8f8f8;
    color: #000000;
    text-decoration: none;
    font-family: "Fira Code", monospace;
    font-size: 10px;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
  }

  .catham-tag:hover {
    background-color: #000000;
    color: #ffffff;
    border-color: #000000;
  }

  .catham-navigation {
    margin-top: 60px;
    padding: 40px 0;
    background-color: #f8f8f8;
    border-radius: 4px;
  }

  .catham-nav-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .catham-nav-link {
    font-family: "Fira Code", monospace;
    font-size: 11px;
    color: #000000;
    text-decoration: underline;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: opacity 0.3s ease;
  }

  .catham-nav-link:hover {
    opacity: 0.7;
    color: #000000;
  }

  .catham-back-link {
    margin-top: 60px;
    text-align: center;
  }

  .catham-back-link a {
    font-family: "Old Standard TT", serif;
    font-size: 1.2rem;
    color: #000000;
    text-decoration: none;
    font-weight: 700;
    letter-spacing: 0.02em;
    transition: opacity 0.3s ease;
  }

  .catham-back-link a:hover {
    opacity: 0.7;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .catham-article-container {
      padding: 40px 20px;
    }

    .catham-article-title {
      font-size: 2rem;
    }

    .catham-article-text h2 {
      font-size: 1.6rem;
      padding-left: 20px;
    }

    .catham-byline {
      display: none;
    }

    .catham-nav-links {
      flex-direction: column;
      gap: 20px;
    }
  }

  @media (max-width: 480px) {
    .catham-article-title {
      font-size: 1.8rem;
    }

    .catham-article-text h2 {
      font-size: 1.4rem;
    }
  }
</style>

{%- assign new_comment = false -%} {%- if comment and comment.created_at -%} {%-
assign new_comment = true -%} {%- assign new_comment_id = comment.id -%} {%-
endif -%} {%- if new_comment -%} {%- assign duplicate_comment = false %} {%- for
comment in article.comments -%} {%- if comment.id == new_comment_id -%} {%-
assign duplicate_comment = true %} {%- break -%} {%- endif -%} {%- endfor -%}
{%- if duplicate_comment -%} {%- assign number_of_comments =
article.comments_count -%} {%- else -%} {%- assign number_of_comments =
article.comments_count | plus: 1 -%} {%- endif -%} {%- else -%} {%- assign
number_of_comments = article.comments_count -%} {%- endif -%}

<article
  class="catham-article-container"
  itemscope
  itemtype="http://schema.org/Article"
>
  <!-- Article Header -->
  <header class="catham-article-header">
    <div class="catham-header-meta">
      <span class="issue-number"
        >EDITORIAL N°{{ article.id | modulo: 100 | plus: 1 }}</span
      >
      <span class="publication-date"
        >{{ article.published_at | date: "%B %Y" | upcase }}</span
      >
    </div>

    <h1 class="catham-article-title" itemprop="headline">
      {{ article.title }}
    </h1>

    {%- if article.excerpt -%}
    <div class="catham-article-subtitle">
      {{ article.excerpt | strip_html | truncate: 60 }}
    </div>
    {%- endif -%}

    <div class="catham-byline">
      <span class="author" itemprop="author"
        >By {{ article.author | default: "Cathám Beauty Editorial" }}</span
      >
      <span class="read-time"
        >{{ article.content | strip_html | split: ' ' | size | divided_by: 200 |
        plus: 1 }} min read</span
      >
    </div>
  </header>

  <!-- Article Content -->
  <section class="catham-article-content">
    <div class="catham-article-text" itemprop="articleBody">
      {{ article.content }}
    </div>
  </section>
  <!-- Article Footer -->
  <footer class="catham-article-footer">
    {%- if article.tags.size > 0 -%}
    <div class="catham-tags">
      <span class="tags-label">Tagged:</span>
      {% for tag in article.tags %}
      <a href="{{ blog.url }}/tagged/{{ tag | handle }}" class="catham-tag"
        >{{ tag }}</a
      >
      {% endfor %}
    </div>
    {%- endif -%} {%- if section.settings.social_sharing_blog -%}
    <div class="catham-social-sharing">
      {%- include 'social-sharing', share_title: article.title, share_permalink:
      article.url, share_image: article.image -%}
    </div>
    {%- endif -%}
  </footer>
  {%- if section.settings.blog_show_previous_and_next_buttons -%}
  <nav class="catham-navigation">
    <div class="catham-nav-links">
      {%- if blog.previous_article -%}
      <a href="{{ blog.previous_article }}" class="catham-nav-link">
        ← {{ 'blogs.article.pre_post' | t }}
      </a>
      {%- else -%}
      <span></span>
      {%- endif -%} {%- if blog.next_article -%}
      <a href="{{ blog.next_article }}" class="catham-nav-link">
        {{ 'blogs.article.next_post' | t }} →
      </a>
      {%- else -%}
      <span></span>
      {%- endif -%}
    </div>
  </nav>
  {%- endif -%}
  <!-- Back to Blog Link -->
  <div class="catham-back-link">
    <a href="{{ blog.url }}">
      ← {{ 'blogs.article.back_to_blog' | t: title: blog.title }}
    </a>
  </div>

</article>

{% schema %}
{
  "name": "Cathám Article Template",
  "settings": [
    {
      "type": "checkbox",
      "id": "social_sharing_blog",
      "label": "Enable blog post sharing",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_previous_and_next_buttons",
      "label": "Show next/previous buttons",
      "default": true
    }
  ]
}
{% endschema %}
</article>
