<!-- Cathám Magazine-Style Article Template -->
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap" />

<style>
  /* Cathám Magazine Article Styling */
  .catham-article-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 80px 50px;
    background-color: #ffffff;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 12px;
    line-height: 1.65;
    color: #1a1a1a;
    box-shadow: 0 0 60px rgba(0, 0, 0, 0.08);
    position: relative;
  }

  .catham-article-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 50px;
    right: 50px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #2c2c2c 50%, transparent 100%);
  }

  /* Article Header */
  .catham-article-header {
    text-align: center;
    margin-bottom: 60px;
    padding-bottom: 40px;
    position: relative;
    background-color: transparent;
    color: #1a1a1a;
    padding-top: 0;
  }

  .catham-article-header::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #1a1a1a 50%, transparent 100%);
  }

  .catham-header-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    font-family: "Fira Code", monospace;
    font-size: 12px;
    letter-spacing: 2px;
    font-weight: 500;
    color: #666;
  }

  .catham-article-title {
    font-family: "Old Standard TT", serif;
    font-size: 3.2rem;
    font-weight: 700;
    line-height: 0.95;
    margin-bottom: 25px;
    letter-spacing: -0.03em;
    text-transform: uppercase;
    position: relative;
    color: #000000;
  }

  .catham-article-title::before {
    content: "";
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #2c2c2c;
  }

  .catham-article-subtitle {
    font-family: "Old Standard TT", serif;
    font-size: 1.4rem;
    font-style: italic;
    margin-bottom: 40px;
    color: #666;
    letter-spacing: 0.08em;
    text-transform: lowercase;
  }

  .catham-byline {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-family: "Fira Code", monospace;
    font-size: 14px;
    color: #666;
  }

  /* Article Content */
  .catham-article-content {
    padding: 0;
    max-width: 100%;
    margin: 0 auto;
  }

  .catham-lead-paragraph {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 40px;
    color: #333333;
    font-weight: 400;
    text-align: left;
    font-family: Helvetica, Arial, sans-serif;
  }

  .catham-lead-paragraph:first-letter {
    font-family: "Old Standard TT", serif;
    font-size: 4rem;
    font-weight: 700;
    line-height: 1;
    float: left;
    margin: 8px 8px 0 0;
    color: #1a1a1a;
  }

  .catham-article-text {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 12px;
    margin-bottom: 25px;
    line-height: 1.7;
    max-width: 100%;
    color: #333333;
    text-align: left;
  }

  .catham-article-text h2 {
    font-family: "Old Standard TT", serif;
    font-size: 2.4rem;
    font-weight: 700;
    color: #000000;
    margin: 80px 0 40px;
    letter-spacing: -0.02em;
    line-height: 1.1;
    position: relative;
    padding-left: 30px;
  }

  .catham-article-text h2::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #2c2c2c 0%, #1a1a1a 100%);
  }

  .catham-article-text p {
    margin-bottom: 20px;
  }

  .catham-article-text strong {
    font-weight: 600;
    color: #000000;
  }

  .catham-article-text a {
    color: #000000;
    text-decoration: underline;
    font-weight: 600;
    transition: opacity 0.3s ease;
  }

  .catham-article-text a:hover {
    opacity: 0.7;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .catham-article-container {
      padding: 40px 20px;
    }

    .catham-article-title {
      font-size: 2rem;
    }

    .catham-article-text h2 {
      font-size: 1.6rem;
      padding-left: 20px;
    }

    .catham-lead-paragraph {
      font-size: 1rem;
    }

    .catham-byline {
      display: none;
    }
  }

  @media (max-width: 480px) {
    .catham-article-title {
      font-size: 1.8rem;
    }

    .catham-article-text h2 {
      font-size: 1.4rem;
    }
  }
</style>

<article class="catham-article-container">
  <!-- Article Header -->
  <header class="catham-article-header">
    <div class="catham-header-meta">
      <span class="issue-number">EDITORIAL N°{{ article.id | modulo: 100 | plus: 1 }}</span>
      <span class="publication-date">{{ article.published_at | date: "%B %Y" | upcase }}</span>
    </div>
    
    <h1 class="catham-article-title">{{ article.title }}</h1>
    
    {%- if article.excerpt -%}
    <div class="catham-article-subtitle">{{ article.excerpt | strip_html | truncate: 60 }}</div>
    {%- endif -%}
    
    <div class="catham-byline">
      <span class="author">By {{ article.author | default: "Cathám Beauty Editorial" }}</span>
      <span class="read-time">{{ article.content | strip_html | split: ' ' | size | divided_by: 200 | plus: 1 }} min read</span>
    </div>
  </header>

  <!-- Article Content -->
  <section class="catham-article-content">
    <div class="catham-article-text">
      {{ article.content }}
    </div>
  </section>
</article>
