<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firming Bakuchiol Oil Serum - Ingredient Infographic</title>
    <link href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="infographic-container">
        <!-- Header -->
        <div class="header-section">
            <div class="product-title">FIRMING BAKUCHIOL OIL SERUM</div>
            <div class="subtitle">CLEAN LUXURY FORMULATION</div>
            <div class="divider-line"></div>
        </div>

        <!-- Hero Ingredient -->
        <div class="hero-ingredient">
            <div class="ingredient-number">N°1</div>
            <div class="ingredient-content">
                <div class="ingredient-name">BAKUCHIOL</div>
                <div class="ingredient-subtitle">Plant Retinol Alternative</div>
                <div class="ingredient-benefit">Stimulates collagen renewal without irritation. Pregnancy-safe anti-aging that works with your skin's natural rhythm.</div>
            </div>
            <div class="ingredient-icon">🌿</div>
        </div>

        <!-- Supporting Ingredients Grid -->
        <div class="ingredients-grid">
            <div class="ingredient-card">
                <div class="card-number">N°2</div>
                <div class="card-name">JOJOBA SEED OIL</div>
                <div class="card-subtitle">Biomimetic Base</div>
                <div class="card-benefit">Identical to skin's natural sebum. Delivers actives deep into skin without clogging pores.</div>
                <div class="card-icon">💧</div>
            </div>

            <div class="ingredient-card">
                <div class="card-number">N°3</div>
                <div class="card-name">NORDIC BERRY COMPLEX</div>
                <div class="card-subtitle">Antioxidant Powerhouse</div>
                <div class="card-benefit">Strawberry, Raspberry & Blueberry seed oils provide vitamin C-rich environmental protection.</div>
                <div class="card-icon">🫐</div>
            </div>

            <div class="ingredient-card">
                <div class="card-number">N°4</div>
                <div class="card-name">EVENING PRIMROSE OIL</div>
                <div class="card-subtitle">Barrier Repair Specialist</div>
                <div class="card-benefit">Rich in GLA omega-6 fatty acids. Calms inflammation and strengthens skin barrier function.</div>
                <div class="card-icon">🌸</div>
            </div>

            <div class="ingredient-card">
                <div class="card-number">N°5</div>
                <div class="card-name">ROSEHIP FRUIT OIL</div>
                <div class="card-subtitle">Natural Vitamin A + C</div>
                <div class="card-benefit">Accelerates skin renewal and supports natural collagen production. Gentle yet transformative.</div>
                <div class="card-icon">🌹</div>
            </div>
        </div>

        <!-- Key Benefits Summary -->
        <div class="benefits-section">
            <div class="benefits-title">KEY BENEFITS</div>
            <div class="benefits-grid">
                <div class="benefit-item">
                    <div class="benefit-icon">✨</div>
                    <div class="benefit-text">Firms without irritation</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🛡️</div>
                    <div class="benefit-text">Strengthens skin barrier</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🌿</div>
                    <div class="benefit-text">Pregnancy-safe formula</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">💎</div>
                    <div class="benefit-text">Luxury oil texture</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-section">
            <div class="brand-name">CATHÁM</div>
            <div class="brand-tagline">Where Science Meets Skin Intelligence</div>
        </div>
    </div>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Fira Code', monospace;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 40px 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .infographic-container {
            max-width: 800px;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
        }

        /* Header */
        .header-section {
            background: linear-gradient(135deg, #000000 0%, #2c2c2c 100%);
            color: #ffffff;
            padding: 50px 40px;
            text-align: center;
        }

        .product-title {
            font-family: 'Old Standard TT', serif;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: 3px;
            margin-bottom: 15px;
        }

        .subtitle {
            font-size: 14px;
            letter-spacing: 2px;
            opacity: 0.8;
            margin-bottom: 30px;
        }

        .divider-line {
            width: 60px;
            height: 2px;
            background: #ffffff;
            margin: 0 auto;
        }

        /* Hero Ingredient */
        .hero-ingredient {
            padding: 50px 40px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            display: flex;
            align-items: center;
            gap: 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .ingredient-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Old Standard TT', serif;
            font-size: 24px;
            font-weight: 700;
            flex-shrink: 0;
        }

        .ingredient-content {
            flex: 1;
        }

        .ingredient-name {
            font-family: 'Old Standard TT', serif;
            font-size: 28px;
            font-weight: 700;
            color: #000000;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }

        .ingredient-subtitle {
            font-size: 14px;
            color: #ff6b6b;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .ingredient-benefit {
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
        }

        .ingredient-icon {
            font-size: 40px;
            flex-shrink: 0;
        }

        /* Ingredients Grid */
        .ingredients-grid {
            padding: 50px 40px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }

        .ingredient-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 30px;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .ingredient-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        }

        .card-number {
            position: absolute;
            top: -15px;
            left: 20px;
            width: 30px;
            height: 30px;
            background: #000000;
            color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
        }

        .card-name {
            font-family: 'Old Standard TT', serif;
            font-size: 18px;
            font-weight: 700;
            color: #000000;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .card-subtitle {
            font-size: 12px;
            color: #666666;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 0.5px;
        }

        .card-benefit {
            font-size: 13px;
            line-height: 1.5;
            color: #333333;
            margin-bottom: 15px;
        }

        .card-icon {
            font-size: 24px;
            text-align: right;
        }

        /* Benefits Section */
        .benefits-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 50px 40px;
        }

        .benefits-title {
            font-family: 'Old Standard TT', serif;
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            letter-spacing: 2px;
            color: #000000;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            gap: 15px;
            background: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .benefit-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .benefit-text {
            font-size: 14px;
            font-weight: 600;
            color: #333333;
        }

        /* Footer */
        .footer-section {
            background: #000000;
            color: #ffffff;
            padding: 40px;
            text-align: center;
        }

        .brand-name {
            font-family: 'Old Standard TT', serif;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: 3px;
            margin-bottom: 10px;
        }

        .brand-tagline {
            font-size: 12px;
            letter-spacing: 1px;
            opacity: 0.8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .infographic-container {
                margin: 20px;
            }

            .header-section,
            .hero-ingredient,
            .ingredients-grid,
            .benefits-section,
            .footer-section {
                padding: 30px 20px;
            }

            .product-title {
                font-size: 24px;
            }

            .hero-ingredient {
                flex-direction: column;
                text-align: center;
            }

            .ingredients-grid,
            .benefits-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .ingredient-name {
                font-size: 24px;
            }

            .card-name {
                font-size: 16px;
            }
        }
    </style>
</body>
</html>
