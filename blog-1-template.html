<!-- Cathám Magazine-Style Blog Template -->
<link
  rel="stylesheet"
  href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
/>

<style>
  /* Cathám Magazine Blog Styling */
  .catham-blog-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 80px 50px;
    background-color: #ffffff;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 12px;
    line-height: 1.65;
    color: #1a1a1a;
  }

  .catham-blog-header {
    text-align: center;
    margin-bottom: 80px;
    padding-bottom: 40px;
    border-bottom: 2px solid #e8e8e8;
  }

  .catham-blog-title {
    font-family: "Old Standard TT", serif;
    font-size: 3.5rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 20px;
    letter-spacing: -0.02em;
    text-transform: uppercase;
  }

  .catham-blog-subtitle {
    font-family: "Fira Code", monospace;
    font-size: 14px;
    color: #666;
    letter-spacing: 2px;
    text-transform: uppercase;
  }

  .catham-articles-grid {
    display: grid;
    gap: 60px;
    margin-bottom: 80px;
  }

  .catham-article-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
    padding: 40px 0;
    border-bottom: 1px solid #e8e8e8;
  }

  .catham-article-card:last-child {
    border-bottom: none;
  }

  .catham-article-content {
    order: 1;
  }

  .catham-article-image {
    order: 2;
    position: relative;
  }

  .catham-article-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border: 8px solid #ffffff;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.18);
    transition: transform 0.4s ease;
  }

  .catham-article-image img:hover {
    transform: scale(1.02);
  }

  .catham-article-meta {
    font-family: "Fira Code", monospace;
    font-size: 11px;
    color: #666;
    margin-bottom: 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
  }

  .catham-article-title {
    font-family: "Old Standard TT", serif;
    font-size: 2.2rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 20px;
    line-height: 1.1;
    letter-spacing: -0.02em;
  }

  .catham-article-title a {
    color: inherit;
    text-decoration: none;
    transition: opacity 0.3s ease;
  }

  .catham-article-title a:hover {
    opacity: 0.7;
  }

  .catham-article-excerpt {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 12px;
    line-height: 1.7;
    color: #333333;
    margin-bottom: 25px;
  }

  .catham-read-more {
    font-family: "Fira Code", monospace;
    font-size: 11px;
    color: #000000;
    text-decoration: underline;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: opacity 0.3s ease;
  }

  .catham-read-more:hover {
    opacity: 0.7;
    color: #000000;
  }
</style>
<!-- Cathám Magazine Blog Layout -->
<div class="catham-blog-container">
  <!-- Blog Header -->
  <header class="catham-blog-header">
    <h1 class="catham-blog-title">{{ blog.title | default: "Editorial" }}</h1>
    <div class="catham-blog-subtitle">Cathám Beauty Editorial</div>
  </header>

  <!-- Articles Grid -->
  {%- assign limit = section.settings.limit | minus:1 -%} {%- paginate
  blog.articles by limit -%}

  <div class="catham-articles-grid">
    {%- for article in blog.articles -%}
    <article class="catham-article-card">
      <!-- Article Content -->
      <div class="catham-article-content">
        <div class="catham-article-meta">
          {{ article.published_at | date: "%B %d, %Y" }} • {{ article.author }}
        </div>

        <h2 class="catham-article-title">
          <a href="{{ article.url }}">{{ article.title }}</a>
        </h2>

        <div class="catham-article-excerpt">
          {{ article.excerpt | strip_html | truncate: 200 }}
        </div>

        <a href="{{ article.url }}" class="catham-read-more">
          Read Full Article
        </a>
      </div>

      <!-- Article Image -->
      <div class="catham-article-image">
        {%- if article.image -%}
        <img
          src="{{ article.image | img_url: '600x400' }}"
          alt="{{ article.image.alt | default: article.title }}"
          loading="lazy"
        />
        {%- else -%}
        <img
          src="{{ 'placeholder-image.jpg' | asset_url }}"
          alt="{{ article.title }}"
          loading="lazy"
        />
        {%- endif -%}
      </div>
    </article>
    {%- endfor -%}
  </div>

  <!-- Pagination -->
  {%- if paginate.pages > 1 -%}
  <div class="catham-pagination">
    {{ paginate | default_pagination | replace: '&laquo; Previous', '&larr;' |
    replace: 'Next &raquo;', '&rarr;' }}
  </div>
  {%- endif -%} {%- endpaginate -%}
</div>

{% schema %} { "name": "Blog template 1", "settings": [ { "type": "range", "id":
"limit", "min": 6, "max": 50, "step": 1, "label": "Limit", "default": 8 }, {
"type": "header", "content": "Sidebar" }, { "type": "checkbox", "id":
"sidebar_enable", "label": "Enable", "default": true }, { "type": "select",
"id": "sidebar_position", "label": "Position", "default": "right", "options": [
{ "value": "left", "label": "Left" }, { "value": "right", "label": "Right" } ] }
] } {% endschema %}
