<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Build Your Skincare Routine | Cathám</title>
  <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;600&family=Montserrat:wght@300;400;500&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Montserrat', sans-serif;
      margin: 0;
      padding: 0;
      color: #333;
      background-color: #fff;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 60px 20px;
    }
    
    .brand-header {
      text-align: center;
      text-transform: uppercase;
      letter-spacing: 2px;
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
    }
    
    h1, h2, h3, h4 {
      font-family: 'Cormorant Garamond', serif;
      font-weight: 400;
    }
    
    h1 {
      text-align: center;
      font-size: 60px;
      margin: 0 0 20px 0;
      text-transform: uppercase;
      letter-spacing: 2px;
    }
    
    .subtitle {
      text-align: center;
      font-family: 'Cormorant Garamond', serif;
      font-size: 20px;
      font-style: italic;
      color: #666;
      margin-bottom: 80px;
    }
    
    .step {
      display: none;
      margin-bottom: 60px;
    }
    
    .step.active {
      display: block;
    }
    
    .step-title {
      text-align: center;
      font-size: 36px;
      margin-bottom: 20px;
    }
    
    .step-description {
      text-align: center;
      max-width: 600px;
      margin: 0 auto 50px;
      color: #666;
      line-height: 1.6;
    }
    
    .options {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
      margin: 30px 0;
    }
    
    .option {
      width: 280px;
      border: 1px solid #e0e0e0;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #fff;
      padding: 30px;
      text-align: left;
    }
    
    .option:hover {
      border-color: #999;
    }
    
    .option.selected {
      border-color: #333;
      background-color: #fafafa;
    }
    
    .option h3 {
      margin: 0 0 15px 0;
      font-size: 24px;
    }
    
    .option p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
      color: #666;
    }
    
    .navigation {
      display: flex;
      justify-content: center;
      margin-top: 50px;
    }
    
    button {
      background-color: #333;
      color: white;
      border: none;
      padding: 12px 40px;
      font-size: 14px;
      cursor: pointer;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: background-color 0.3s ease;
    }
    
    button:hover {
      background-color: #555;
    }
    
    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    
    button.secondary {
      background-color: transparent;
      color: #333;
      border: 1px solid #333;
      margin-right: 20px;
    }
    
    button.secondary:hover {
      background-color: #f5f5f5;
    }
    
    .results {
      display: none;
    }
    
    .results.active {
      display: block;
    }
    
    /* Routine display styles */
    .routine-section {
      margin-bottom: 60px;
    }
    
    .routine-title {
      font-size: 28px;
      text-align: center;
      margin-bottom: 30px;
      font-family: 'Cormorant Garamond', serif;
    }
    
    .product-grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
    }
    
    .product-card {
      width: 250px;
      border: 1px solid #e0e0e0;
      padding: 20px;
      text-align: center;
    }
    
    .product-image {
      width: 100%;
      height: 200px;
      background-size: cover;
      background-position: center;
      margin-bottom: 15px;
    }
    
    .product-name {
      font-family: 'Cormorant Garamond', serif;
      font-size: 20px;
      margin-bottom: 10px;
    }
    
    .product-description {
      font-size: 14px;
      color: #666;
      margin-bottom: 15px;
      line-height: 1.4;
    }
    
    .product-price {
      font-weight: 500;
      margin-bottom: 15px;
    }
    
    .add-to-cart {
      width: 100%;
      padding: 10px;
      background-color: #333;
      color: white;
      border: none;
      cursor: pointer;
      text-transform: uppercase;
      font-size: 12px;
      letter-spacing: 1px;
    }
    
    .add-to-cart:hover {
      background-color: #555;
    }
    
    .routine-explanation {
      max-width: 700px;
      margin: 0 auto 40px;
      padding: 30px;
      background-color: #f9f9f9;
      text-align: center;
    }
    
    .routine-explanation h3 {
      margin-top: 0;
      font-size: 24px;
    }
    
    .routine-explanation p {
      line-height: 1.6;
    }
    
    .restart-button {
      display: block;
      width: 200px;
      margin: 40px auto;
      text-align: center;
      padding: 12px 0;
      background-color: transparent;
      color: #333;
      border: 1px solid #333;
      cursor: pointer;
      text-transform: uppercase;
      font-size: 14px;
      letter-spacing: 1px;
    }
    
    .restart-button:hover {
      background-color: #f5f5f5;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="brand-header">Cathám | Clean Beauty Revolution</div>
    <h1>Build Your Routine</h1>
    <div class="subtitle">Personalized skincare tailored to your unique needs</div>
    
    <!-- Step 1: Skin Type -->
    <div id="step1" class="step active">
      <h2 class="step-title">What's your skin type?</h2>
      <div class="step-description">Select the option that best describes your skin's natural state.</div>
      <div class="options">
        <div class="option" data-value="dry">
          <h3>Dry</h3>
          <p>Your skin often feels tight, flaky, or rough to the touch. You may experience occasional irritation or redness.</p>
        </div>
        <div class="option" data-value="oily">
          <h3>Oily</h3>
          <p>Your skin appears shiny, especially in the T-zone. You may experience enlarged pores and occasional breakouts.</p>
        </div>
        <div class="option" data-value="combination">
          <h3>Combination</h3>
          <p>You have an oily T-zone (forehead, nose, chin) but dry or normal cheeks. Different areas need different care.</p>
        </div>
      </div>
      <div class="navigation">
        <button id="next1" disabled>Continue</button>
      </div>
    </div>
    
    <!-- Step 2: Skin Goal -->
    <div id="step2" class="step">
      <h2 class="step-title">What's your main skin goal?</h2>
      <div class="step-description">Choose the primary concern you'd like to address with your skincare routine.</div>
      <div class="options">
        <div class="option" data-value="anti-aging">
          <h3>Anti-Aging</h3>
          <p>Target fine lines, wrinkles, and loss of firmness with collagen-supporting ingredients.</p>
        </div>
        <div class="option" data-value="hydration">
          <h3>Hydration</h3>
          <p>Restore moisture balance and strengthen your skin's natural barrier function.</p>
        </div>
        <div class="option" data-value="brightening">
          <h3>Brightening</h3>
          <p>Even skin tone, reduce dark spots, and restore radiance to dull complexion.</p>
        </div>
      </div>
      <div class="navigation">
        <button id="back2" class="secondary">Back</button>
        <button id="next2" disabled>See My Routine</button>
      </div>
    </div>
    
    <!-- Results will be inserted here by JavaScript -->
    <div id="results" class="results"></div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Variables to store user selections
      let skinType = '';
      let skinGoal = '';
      
      // Product database with real product IDs
      const products = {
        cleansers: [{
          id: '15098440745295',
          name: 'Pure Skin Cleansing Mousse',
          description: 'A gentle, non-stripping formula enriched with aloe, sage, and blackberry.',
          price: '€32',
          image: 'https://catham.eu/cdn/shop/files/pure_skin_cleanser-Photoroom_feb70507-d7aa-4019-93f4-71f04481fad0_1200x.jpg?v=1745680116'
        }],
        treatments: [{
          id: '15097914196303',
          name: 'Collagen Lifting Serum',
          description: 'Multi-molecular Hyaluronic Acid, Vitamin C, Elderberry, and Watercress.',
          price: '€48',
          image: 'https://catham.eu/cdn/shop/files/collagen_lifting_serum-Photoroom_1200x.jpg?v=1745680431'
        }, {
          id: '15260345631055',
          name: 'Anti-Puffiness Gel',
          description: 'Reduces puffiness with 1% Caffeine complex.',
          price: '€45',
          image: 'https://catham.eu/cdn/shop/files/anti_puffiness_gel-Photoroom_902ae961-cf4f-4cc7-a705-ade1e17d5c71_1200x.jpg?v=1745679431'
        }],
        moisturizers: [{
          id: '15185622434127',
          name: 'Anti-Aging Day Cream',
          description: 'Marine actives, Aloe, Shea Butter, and Avocado Oil.',
          price: '€44',
          image: 'https://catham.eu/cdn/shop/files/anti_aging_day_cream-Photoroom_4be1d48c-e6a9-44a2-a4a2-076cae479ab3_1200x.jpg?v=1745679764'
        }, {
          id: '15215664005455',
          name: 'Niacinamide 5% Gel Moisturiser',
          description: '5% Niacinamide helps regulate oil and refine pores.',
          price: '€40',
          image: 'https://catham.eu/cdn/shop/files/niacimide-Photoroom_1200x.jpg?v=1745680263'
        }],
        protectors: [{
          id: '15183920169295',
          name: 'Tinted Sunscreen SPF30',
          description: 'Broad-spectrum UV protection with Raspberry Seed and Sea Buckthorn.',
          price: '€42',
          image: 'https://catham.eu/cdn/shop/files/tinted_sunscreen-Photoroom_fe3f8f0d-f7e2-4381-b803-aa7692c945a4_1200x.jpg?v=1745679986'
        }, {
          id: '15185609326927',
          name: 'Firming Bakuchiol Oil Serum',
          description: 'Bakuchiol stimulates collagen renewal while botanical oils seal in moisture.',
          price: '€48',
          image: 'https://catham.eu/cdn/shop/files/firming_bakuchiol_oil_serum_1200x.png?v=1745679863'
        }, {
          id: '15185622401359',
          name: 'Ceramide Hydrating Night Cream',
          description: 'Ceramides, Sea Buckthorn, and Peony Root extract rebuild the lipid barrier.',
          price: '€46',
          image: 'https://catham.eu/cdn/shop/files/ceramide_hydrating_night_cream-Photoroom_706eb691-f9a9-4462-a62c-538ba134be95_1200x.jpg?v=1745680049'
        }]
      };
      
      // Add event listeners to options
      document.querySelectorAll('.option').forEach(option => {
        option.addEventListener('click', function() {
          // Remove selected class from all options in the same step
          const parentStep = this.closest('.step');
          parentStep.querySelectorAll('.option').forEach(opt => {
            opt.classList.remove('selected');
          });
          
          // Add selected class to clicked option
          this.classList.add('selected');
          
          // Enable next button
          if (parentStep.id === 'step1') {
            skinType = this.getAttribute('data-value');
            document.getElementById('next1').disabled = false;
          } else if (parentStep.id === 'step2') {
            skinGoal = this.getAttribute('data-value');
            document.getElementById('next2').disabled = false;
          }
        });
      });
      
      // Navigation buttons
      document.getElementById('next1').addEventListener('click', function() {
        document.getElementById('step1').classList.remove('active');
        document.getElementById('step2').classList.add('active');
      });
      
      document.getElementById('back2').addEventListener('click', function() {
        document.getElementById('step2').classList.remove('active');
        document.getElementById('step1').classList.add('active');
      });
      
      document.getElementById('next2').addEventListener('click', function() {
        generateResults();
        document.getElementById('step2').classList.remove('active');
        document.getElementById('results').classList.add('active');
      });
      
      // Function to generate results based on selections
      function generateResults() {
        const resultsDiv = document.getElementById('results');
        
        // Create a default routine (simplified for this example)
        let morningProducts = [
          {category: 'cleansers', id: '15098440745295'}, // Pure Skin Cleansing Mousse
          {category: 'treatments', id: '15097914196303'}, // Collagen Lifting Serum
          {category: 'moisturizers', id: '15185622434127'}, // Anti-Aging Day Cream
          {category: 'protectors', id: '15183920169295'} // Tinted Sunscreen SPF30
        ];
        
        let eveningProducts = [
          {category: 'cleansers', id: '15098440745295'}, // Pure Skin Cleansing Mousse
          {category: 'treatments', id: '15097914196303'}, // Collagen Lifting Serum
          {category: 'protectors', id: '15185622401359'} // Ceramide Night Cream
        ];
        
        // Customize based on skin type and goal
        if (skinType === 'oily') {
          // Replace moisturizer with oil-free option
          morningProducts[2] = {category: 'moisturizers', id: '15215664005455'}; // Niacinamide Gel
          
          // Add anti-puffiness for oily skin
          if (skinGoal === 'anti-aging') {
            eveningProducts[1] = {category: 'treatments', id: '15260345631055'}; // Anti-Puffiness Gel
          }
        }
        
        // Create explanation text based on selections
        let explanationText = '';
        if (skinType === 'dry') {
          explanationText = 'Your routine focuses on deep hydration and barrier repair to address your dry skin concerns.';
          if (skinGoal === 'anti-aging') {
            explanationText += ' We've included collagen-supporting ingredients to target fine lines while maintaining moisture.';
          } else if (skinGoal === 'hydration') {
            explanationText += ' We've maximized hydrating ingredients to ensure lasting comfort and plumpness.';
          } else if (skinGoal === 'brightening') {
            explanationText += ' We've balanced brightening ingredients with moisture-rich formulas to prevent irritation.';
          }
        } else if (skinType === 'oily') {
          explanationText = 'Your routine balances oil control with necessary hydration to keep your skin balanced.';
          if (skinGoal === 'anti-aging') {
            explanationText += ' We've selected lightweight, non-comedogenic anti-aging ingredients that won't clog pores.';
          } else if (skinGoal === 'hydration') {
            explanationText += ' We've included oil-free hydrators that won't contribute to excess shine.';
          } else if (skinGoal === 'brightening') {
            explanationText += ' We've incorporated brightening ingredients that also help regulate oil production.';
          }
        } else if (skinType === 'combination') {
          explanationText = 'Your routine addresses multiple concerns with balanced formulas for your combination skin.';
          if (skinGoal === 'anti-aging') {
            explanationText += ' We've selected versatile anti-aging ingredients that work for both dry and oily areas.';
          } else if (skinGoal === 'hydration') {
            explanationText += ' We've included targeted hydration that won't overwhelm oilier zones.';
          } else if (skinGoal === 'brightening') {
            explanationText += ' We've balanced brightening ingredients that work harmoniously across different skin zones.';
          }
        }
        
        // Create results HTML
        let resultsHTML = `
          <h2 class="step-title">Your Personalized Routine</h2>
          
          <div class="routine-explanation">
            <h3>Why This Routine Works For You</h3>
            <p>${explanationText}</p>
          </div>
          
          <div class="routine-section">
            <h3 class="routine-title">Morning Routine</h3>
            <div class="product-grid">
        `;
        
        // Add morning products
        morningProducts.forEach(item => {
          const product = products[item.category].find(p => p.id === item.id);
          if (product) {
            resultsHTML += `
              <div class="product-card">
                <div class="product-image" style="background-image: url('${product.image}')"></div>
                <div class="product-name">${product.name}</div>
                <div class="product-description">${product.description}</div>
                <div class="product-price">${product.price}</div>
                <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
              </div>
            `;
          }
        });
        
        resultsHTML += `
            </div>
          </div>
          
          <div class="routine-section">
            <h3 class="routine-title">Evening Routine</h3>
            <div class="product-grid">
        `;
        
        // Add evening products
        eveningProducts.forEach(item => {
          const product = products[item.category].find(p => p.id === item.id);
          if (product) {
            resultsHTML += `
              <div class="product-card">
                <div class="product-image" style="background-image: url('${product.image}')"></div>
                <div class="product-name">${product.name}</div>
                <div class="product-description">${product.description}</div>
                <div class="product-price">${product.price}</div>
                <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
              </div>
            `;
          }
        });
        
        resultsHTML += `
            </div>
          </div>
          
          <button class="restart-button" id="restart">Start Over</button>
        `;
        
        // Set the HTML
        resultsDiv.innerHTML = resultsHTML;
        
        // Add event listener to restart button
        document.getElementById('restart').addEventListener('click', function() {
          document.getElementById('results').classList.remove('active');
          document.getElementById('step1').classList.add('active');
          // Reset selections
          document.querySelectorAll('.option').forEach(opt => {
            opt.classList.remove('selected');
          });
          document.getElementById('next1').disabled = true;
          document.getElementById('next2').disabled = true;
        });
        
        // Add event listeners to Add to Cart buttons
        document.querySelectorAll('.add-to-cart').forEach(button => {
          button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            // Here you would typically add the product to the cart
            // For this example, we'll just show an alert
            alert(`Product ${productId} added to cart!`);
          });
        });
      }
    });
  </script>
</body>
</html>