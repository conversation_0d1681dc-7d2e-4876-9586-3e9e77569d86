<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Editor - Cathám</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Fira Code', monospace;
            background: #1a1a1a;
            color: #e0e0e0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #2d2d2d;
            padding: 15px 20px;
            border-bottom: 2px solid #444;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #fff;
            font-size: 18px;
            font-weight: bold;
        }

        .controls {
            display: flex;
            gap: 10px;
        }

        .btn {
            background: #4a4a4a;
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Fira Code', monospace;
            font-size: 12px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #5a5a5a;
        }

        .btn.primary {
            background: #007acc;
        }

        .btn.primary:hover {
            background: #0066aa;
        }

        .editor-container {
            flex: 1;
            display: flex;
            position: relative;
        }

        .line-numbers {
            background: #2d2d2d;
            color: #666;
            padding: 20px 10px;
            font-family: 'Fira Code', monospace;
            font-size: 14px;
            line-height: 1.5;
            text-align: right;
            min-width: 60px;
            border-right: 1px solid #444;
            user-select: none;
        }

        .editor {
            flex: 1;
            background: #1a1a1a;
            color: #e0e0e0;
            border: none;
            outline: none;
            padding: 20px;
            font-family: 'Fira Code', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            white-space: pre;
            overflow-wrap: normal;
            overflow-x: auto;
        }

        .editor:focus {
            background: #1e1e1e;
        }

        .status-bar {
            background: #2d2d2d;
            padding: 8px 20px;
            border-top: 1px solid #444;
            font-size: 12px;
            color: #999;
            display: flex;
            justify-content: space-between;
        }

        .file-info {
            display: flex;
            gap: 20px;
        }

        .preview-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            z-index: 1000;
        }

        .preview-window {
            background: #fff;
            margin: 50px auto;
            width: 90%;
            height: 80%;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .preview-header {
            background: #f0f0f0;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-content {
            height: calc(100% - 60px);
            overflow: auto;
            padding: 20px;
        }

        .close-preview {
            background: #ff5f56;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }

        /* Syntax highlighting for HTML */
        .editor {
            tab-size: 2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Cathám Code Editor</h1>
        <div class="controls">
            <button class="btn" onclick="clearEditor()">Clear</button>
            <button class="btn" onclick="copyCode()">Copy All</button>
            <button class="btn" onclick="saveToFile()">Save</button>
            <button class="btn primary" onclick="previewCode()">Preview</button>
        </div>
    </div>

    <div class="editor-container">
        <div class="line-numbers" id="lineNumbers">1</div>
        <textarea 
            class="editor" 
            id="codeEditor" 
            placeholder="Paste or type your code here..."
            spellcheck="false"
            oninput="updateLineNumbers()"
            onscroll="syncScroll()"
        ></textarea>
    </div>

    <div class="status-bar">
        <div class="file-info">
            <span id="lineCount">Lines: 1</span>
            <span id="charCount">Characters: 0</span>
            <span id="wordCount">Words: 0</span>
        </div>
        <div>
            <span>Ready</span>
        </div>
    </div>

    <div class="preview-container" id="previewContainer">
        <div class="preview-window">
            <div class="preview-header">
                <h3>Code Preview</h3>
                <button class="close-preview" onclick="closePreview()">Close</button>
            </div>
            <div class="preview-content" id="previewContent"></div>
        </div>
    </div>

    <script>
        const editor = document.getElementById('codeEditor');
        const lineNumbers = document.getElementById('lineNumbers');
        const lineCount = document.getElementById('lineCount');
        const charCount = document.getElementById('charCount');
        const wordCount = document.getElementById('wordCount');

        function updateLineNumbers() {
            const lines = editor.value.split('\n').length;
            const numbers = Array.from({length: lines}, (_, i) => i + 1).join('\n');
            lineNumbers.textContent = numbers;
            
            // Update status bar
            lineCount.textContent = `Lines: ${lines}`;
            charCount.textContent = `Characters: ${editor.value.length}`;
            wordCount.textContent = `Words: ${editor.value.trim() ? editor.value.trim().split(/\s+/).length : 0}`;
        }

        function syncScroll() {
            lineNumbers.scrollTop = editor.scrollTop;
        }

        function clearEditor() {
            if (confirm('Are you sure you want to clear all content?')) {
                editor.value = '';
                updateLineNumbers();
                editor.focus();
            }
        }

        function copyCode() {
            editor.select();
            document.execCommand('copy');
            
            // Show feedback
            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = 'Copied!';
            btn.style.background = '#28a745';
            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = '#4a4a4a';
            }, 1500);
        }

        function saveToFile() {
            const content = editor.value;
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'catham-code.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function previewCode() {
            const content = editor.value;
            const previewContainer = document.getElementById('previewContainer');
            const previewContent = document.getElementById('previewContent');
            
            if (content.trim().toLowerCase().includes('<html') || content.trim().toLowerCase().includes('<!doctype')) {
                // HTML content - render as HTML
                previewContent.innerHTML = content;
            } else {
                // Other content - show as formatted text
                previewContent.innerHTML = `<pre style="font-family: 'Fira Code', monospace; white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(content)}</pre>`;
            }
            
            previewContainer.style.display = 'block';
        }

        function closePreview() {
            document.getElementById('previewContainer').style.display = 'none';
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Initialize
        updateLineNumbers();
        editor.focus();

        // Handle tab key for indentation
        editor.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = this.selectionStart;
                const end = this.selectionEnd;
                
                this.value = this.value.substring(0, start) + '  ' + this.value.substring(end);
                this.selectionStart = this.selectionEnd = start + 2;
                updateLineNumbers();
            }
        });

        // Close preview with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePreview();
            }
        });
    </script>
</body>
</html>
