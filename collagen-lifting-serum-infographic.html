<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Collagen Lifting Serum - Ingredient Infographic</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="infographic-container">
      <!-- Header -->
      <div class="header-section">
        <div class="product-title">COLLAGEN LIFTING SERUM</div>
        <div class="subtitle">BOTANICAL FIRMING COMPLEX</div>
        <div class="divider-line"></div>
      </div>

      <!-- Hero Ingredient -->
      <div class="hero-ingredient">
        <div class="ingredient-number">N°1</div>
        <div class="ingredient-content">
          <div class="ingredient-name">WATERCRESS EXTRACT</div>
          <div class="ingredient-subtitle">Natural Lifting Agent</div>
          <div class="ingredient-benefit">
            Nutrient-dense botanical rich in vitamins A, C, and K. Stimulates
            collagen production while providing natural lifting and firming
            action for visibly tighter skin.
          </div>
        </div>
        <div class="ingredient-icon">🌿</div>
      </div>

      <!-- Supporting Ingredients Grid -->
      <div class="ingredients-grid">
        <div class="ingredient-card">
          <div class="card-number">N°2</div>
          <div class="card-name">ELDERBERRY EXTRACT</div>
          <div class="card-subtitle">Antioxidant Firming</div>
          <div class="card-benefit">
            Powerful anthocyanins protect existing collagen from breakdown while
            supporting skin's natural firmness and elasticity for a lifted
            appearance.
          </div>
          <div class="card-icon">🫐</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°3</div>
          <div class="card-name">HYALURONIC ACID DUO</div>
          <div class="card-subtitle">Instant Plumping</div>
          <div class="card-benefit">
            Hydrolyzed hyaluronic acid and sodium hyaluronate create immediate
            lifting effect through deep hydration and surface plumping for
            firmer-looking skin.
          </div>
          <div class="card-icon">💧</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°4</div>
          <div class="card-name">SUNFLOWER SEED OIL</div>
          <div class="card-subtitle">Barrier Strengthening</div>
          <div class="card-benefit">
            Rich in vitamin E and linoleic acid. Strengthens skin barrier while
            providing essential fatty acids that support collagen structure and
            skin firmness.
          </div>
          <div class="card-icon">🌻</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°5</div>
          <div class="card-name">SODIUM PCA</div>
          <div class="card-subtitle">Moisture Magnet</div>
          <div class="card-benefit">
            Natural moisturizing factor that attracts and holds moisture,
            creating optimal hydration for enhanced skin elasticity and natural
            lifting effect.
          </div>
          <div class="card-icon">🌊</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°6</div>
          <div class="card-name">Ascorbyl Palmitate</div>
          <div class="card-subtitle">Collagen Support</div>
          <div class="card-benefit">
            Stable vitamin C derivative that supports collagen synthesis and
            provides antioxidant protection for maintained skin firmness and
            resilience.
          </div>
          <div class="card-icon">✨</div>
        </div>
      </div>

      <!-- Key Benefits Summary -->
      <div class="benefits-section">
        <div class="benefits-title">LIFTING BENEFITS</div>
        <div class="benefits-grid">
          <div class="benefit-item">
            <div class="benefit-icon">⬆️</div>
            <div class="benefit-text">Visible lifting effect</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">💪</div>
            <div class="benefit-text">Firms and tightens</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">🌿</div>
            <div class="benefit-text">Botanical collagen boost</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">💧</div>
            <div class="benefit-text">Instant plumping hydration</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">🛡️</div>
            <div class="benefit-text">Protects skin structure</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">✨</div>
            <div class="benefit-text">Lightweight serum texture</div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer-section">
        <div class="brand-name">CATHÁM</div>
        <div class="brand-tagline">Where Science Meets Skin Intelligence</div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Fira Code", monospace;
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
        padding: 40px 20px;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .infographic-container {
        max-width: 900px;
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 25px 70px rgba(76, 175, 80, 0.15);
        overflow: hidden;
        position: relative;
      }

      /* Header */
      .header-section {
        background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
        color: #ffffff;
        padding: 50px 40px;
        text-align: center;
      }

      .product-title {
        font-family: "Old Standard TT", serif;
        font-size: 32px;
        font-weight: 700;
        letter-spacing: 3px;
        margin-bottom: 15px;
      }

      .subtitle {
        font-size: 14px;
        letter-spacing: 2px;
        opacity: 0.9;
        margin-bottom: 30px;
      }

      .divider-line {
        width: 60px;
        height: 2px;
        background: #ffffff;
        margin: 0 auto;
      }

      /* Hero Ingredient */
      .hero-ingredient {
        padding: 50px 40px;
        background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
        display: flex;
        align-items: center;
        gap: 30px;
        border-bottom: 1px solid #c8e6c9;
      }

      .ingredient-number {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Old Standard TT", serif;
        font-size: 24px;
        font-weight: 700;
        flex-shrink: 0;
      }

      .ingredient-content {
        flex: 1;
      }

      .ingredient-name {
        font-family: "Old Standard TT", serif;
        font-size: 28px;
        font-weight: 700;
        color: #388e3c;
        margin-bottom: 8px;
        letter-spacing: 1px;
      }

      .ingredient-subtitle {
        font-size: 14px;
        color: #4caf50;
        font-weight: 600;
        margin-bottom: 15px;
        letter-spacing: 1px;
      }

      .ingredient-benefit {
        font-size: 16px;
        line-height: 1.6;
        color: #333333;
      }

      .ingredient-icon {
        font-size: 40px;
        flex-shrink: 0;
      }

      /* Ingredients Grid */
      .ingredients-grid {
        padding: 50px 40px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
      }

      .ingredient-card {
        background: #ffffff;
        border: 1px solid #c8e6c9;
        border-radius: 15px;
        padding: 25px;
        position: relative;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .ingredient-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(76, 175, 80, 0.2);
      }

      .card-number {
        position: absolute;
        top: -15px;
        left: 20px;
        width: 30px;
        height: 30px;
        background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 700;
      }

      .card-name {
        font-family: "Old Standard TT", serif;
        font-size: 16px;
        font-weight: 700;
        color: #388e3c;
        margin-bottom: 8px;
        letter-spacing: 0.5px;
      }

      .card-subtitle {
        font-size: 11px;
        color: #4caf50;
        font-weight: 600;
        margin-bottom: 12px;
        letter-spacing: 0.5px;
      }

      .card-benefit {
        font-size: 12px;
        line-height: 1.5;
        color: #333333;
        margin-bottom: 12px;
      }

      .card-icon {
        font-size: 20px;
        text-align: right;
      }

      /* Benefits Section */
      .benefits-section {
        background: linear-gradient(135deg, #e8f5e8 0%, #f3e5f5 100%);
        padding: 50px 40px;
      }

      .benefits-title {
        font-family: "Old Standard TT", serif;
        font-size: 24px;
        font-weight: 700;
        text-align: center;
        margin-bottom: 30px;
        letter-spacing: 2px;
        color: #388e3c;
      }

      .benefits-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
      }

      .benefit-item {
        display: flex;
        align-items: center;
        gap: 12px;
        background: #ffffff;
        padding: 18px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(76, 175, 80, 0.1);
      }

      .benefit-icon {
        font-size: 18px;
        flex-shrink: 0;
      }

      .benefit-text {
        font-size: 13px;
        font-weight: 600;
        color: #333333;
      }

      /* Footer */
      .footer-section {
        background: #388e3c;
        color: #ffffff;
        padding: 40px;
        text-align: center;
      }

      .brand-name {
        font-family: "Old Standard TT", serif;
        font-size: 28px;
        font-weight: 700;
        letter-spacing: 3px;
        margin-bottom: 10px;
      }

      .brand-tagline {
        font-size: 12px;
        letter-spacing: 1px;
        opacity: 0.8;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .infographic-container {
          margin: 20px;
        }

        .header-section,
        .hero-ingredient,
        .ingredients-grid,
        .benefits-section,
        .footer-section {
          padding: 30px 20px;
        }

        .product-title {
          font-size: 24px;
        }

        .hero-ingredient {
          flex-direction: column;
          text-align: center;
        }

        .ingredients-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .benefits-grid {
          grid-template-columns: repeat(2, 1fr);
        }

        .ingredient-name {
          font-size: 24px;
        }

        .card-name {
          font-size: 14px;
        }
      }

      @media (max-width: 480px) {
        .benefits-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </body>
</html>
