<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collagen Protection Cream - Ingredient Infographic</title>
    <link href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="infographic-container">
        <!-- Header -->
        <div class="header-section">
            <div class="product-title">COLLAGEN PROTECTION CREAM</div>
            <div class="subtitle">INTENSIVE BARRIER REPAIR TREATMENT</div>
            <div class="divider-line"></div>
        </div>

        <!-- Hero Ingredient -->
        <div class="hero-ingredient">
            <div class="ingredient-number">N°1</div>
            <div class="ingredient-content">
                <div class="ingredient-name">CLOUDBERRY EXTRACT</div>
                <div class="ingredient-subtitle">Arctic Antioxidant Shield</div>
                <div class="ingredient-benefit">Rare Nordic superfruit packed with vitamin C and ellagic acid. Protects existing collagen from environmental damage while supporting natural collagen synthesis.</div>
            </div>
            <div class="ingredient-icon">🫐</div>
        </div>

        <!-- Supporting Ingredients Grid -->
        <div class="ingredients-grid">
            <div class="ingredient-card">
                <div class="card-number">N°2</div>
                <div class="card-name">COCOA BUTTER</div>
                <div class="card-subtitle">Deep Nourishment</div>
                <div class="card-benefit">Rich in natural antioxidants and fatty acids. Creates a protective barrier while delivering intensive moisture for supple, resilient skin.</div>
                <div class="card-icon">🍫</div>
            </div>

            <div class="ingredient-card">
                <div class="card-number">N°3</div>
                <div class="card-name">ARGAN OIL</div>
                <div class="card-subtitle">Liquid Gold Repair</div>
                <div class="card-benefit">Moroccan treasure rich in vitamin E and essential fatty acids. Repairs damaged skin barrier and boosts natural collagen production.</div>
                <div class="card-icon">✨</div>
            </div>

            <div class="ingredient-card">
                <div class="card-number">N°4</div>
                <div class="card-name">SHEA BUTTER</div>
                <div class="card-subtitle">Barrier Restoration</div>
                <div class="card-benefit">African superfat with anti-inflammatory properties. Strengthens skin's protective barrier while soothing irritation and promoting healing.</div>
                <div class="card-icon">🌰</div>
            </div>

            <div class="ingredient-card">
                <div class="card-number">N°5</div>
                <div class="card-name">OLIVE OIL</div>
                <div class="card-subtitle">Mediterranean Elixir</div>
                <div class="card-benefit">Ancient beauty secret rich in squalene and polyphenols. Provides deep hydration while protecting against oxidative stress and collagen breakdown.</div>
                <div class="card-icon">🫒</div>
            </div>
        </div>

        <!-- Key Benefits Summary -->
        <div class="benefits-section">
            <div class="benefits-title">COLLAGEN PROTECTION BENEFITS</div>
            <div class="benefits-grid">
                <div class="benefit-item">
                    <div class="benefit-icon">🛡️</div>
                    <div class="benefit-text">Shields existing collagen</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🔧</div>
                    <div class="benefit-text">Intensive barrier repair</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">💎</div>
                    <div class="benefit-text">Luxuriously rich texture</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🌿</div>
                    <div class="benefit-text">Natural antioxidant power</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">⚡</div>
                    <div class="benefit-text">Supports collagen synthesis</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🌅</div>
                    <div class="benefit-text">All-day protection</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-section">
            <div class="brand-name">CATHÁM</div>
            <div class="brand-tagline">Where Science Meets Skin Intelligence</div>
        </div>
    </div>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Fira Code', monospace;
            background: linear-gradient(135deg, #f3e5ab 0%, #f5f5dc 100%);
            padding: 40px 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .infographic-container {
            max-width: 900px;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 25px 70px rgba(139, 69, 19, 0.15);
            overflow: hidden;
            position: relative;
        }

        /* Header */
        .header-section {
            background: linear-gradient(135deg, #8b4513 0%, #654321 100%);
            color: #ffffff;
            padding: 50px 40px;
            text-align: center;
        }

        .product-title {
            font-family: 'Old Standard TT', serif;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: 3px;
            margin-bottom: 15px;
        }

        .subtitle {
            font-size: 14px;
            letter-spacing: 2px;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .divider-line {
            width: 60px;
            height: 2px;
            background: #ffffff;
            margin: 0 auto;
        }

        /* Hero Ingredient */
        .hero-ingredient {
            padding: 50px 40px;
            background: linear-gradient(135deg, #f3e5ab 0%, #ffffff 100%);
            display: flex;
            align-items: center;
            gap: 30px;
            border-bottom: 1px solid #f0e68c;
        }

        .ingredient-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #daa520 0%, #b8860b 100%);
            color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Old Standard TT', serif;
            font-size: 24px;
            font-weight: 700;
            flex-shrink: 0;
        }

        .ingredient-content {
            flex: 1;
        }

        .ingredient-name {
            font-family: 'Old Standard TT', serif;
            font-size: 28px;
            font-weight: 700;
            color: #8b4513;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }

        .ingredient-subtitle {
            font-size: 14px;
            color: #daa520;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .ingredient-benefit {
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
        }

        .ingredient-icon {
            font-size: 40px;
            flex-shrink: 0;
        }

        /* Ingredients Grid */
        .ingredients-grid {
            padding: 50px 40px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }

        .ingredient-card {
            background: #ffffff;
            border: 1px solid #f0e68c;
            border-radius: 15px;
            padding: 30px;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .ingredient-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(218, 165, 32, 0.2);
        }

        .card-number {
            position: absolute;
            top: -15px;
            left: 20px;
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #b8860b 0%, #8b4513 100%);
            color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
        }

        .card-name {
            font-family: 'Old Standard TT', serif;
            font-size: 18px;
            font-weight: 700;
            color: #8b4513;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .card-subtitle {
            font-size: 12px;
            color: #daa520;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 0.5px;
        }

        .card-benefit {
            font-size: 13px;
            line-height: 1.5;
            color: #333333;
            margin-bottom: 15px;
        }

        .card-icon {
            font-size: 24px;
            text-align: right;
        }

        /* Benefits Section */
        .benefits-section {
            background: linear-gradient(135deg, #f3e5ab 0%, #faf0e6 100%);
            padding: 50px 40px;
        }

        .benefits-title {
            font-family: 'Old Standard TT', serif;
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            letter-spacing: 2px;
            color: #8b4513;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            gap: 15px;
            background: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
        }

        .benefit-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .benefit-text {
            font-size: 14px;
            font-weight: 600;
            color: #333333;
        }

        /* Footer */
        .footer-section {
            background: #654321;
            color: #ffffff;
            padding: 40px;
            text-align: center;
        }

        .brand-name {
            font-family: 'Old Standard TT', serif;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: 3px;
            margin-bottom: 10px;
        }

        .brand-tagline {
            font-size: 12px;
            letter-spacing: 1px;
            opacity: 0.8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .infographic-container {
                margin: 20px;
            }

            .header-section,
            .hero-ingredient,
            .ingredients-grid,
            .benefits-section,
            .footer-section {
                padding: 30px 20px;
            }

            .product-title {
                font-size: 24px;
            }

            .hero-ingredient {
                flex-direction: column;
                text-align: center;
            }

            .ingredients-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .benefits-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .ingredient-name {
                font-size: 24px;
            }

            .card-name {
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .benefits-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
