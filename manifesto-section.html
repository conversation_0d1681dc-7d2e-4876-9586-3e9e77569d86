<!-- manifesto-section.html	 -->

<div class="editorial-magazine">
  <!-- Magazine Header -->
  <header class="magazine-masthead">
    <div class="issue-details">
      <span class="issue-number">N°01</span>
      <span class="issue-date">AUTUMN 2023</span>
    </div>
    <h1 class="magazine-title">Tri-CollagenLift™</h1>
    <div class="magazine-subtitle">THE ARCHITECTURE OF RENEWAL</div>
  </header>
  <!-- Hero Section with Side Texts -->
  <div class="hero-section">
    <div class="hero-container">
      <div class="hero-side-text left-text">
        <div class="side-text-content">
          <div class="side-text-line">TRI-COLLAGENLIFT™</div>
          <div class="side-text-line">SYSTEM</div>
        </div>
      </div>
      <div class="hero-image-container">
        <img
          class="hero-image"
          alt="Collagen boosting skincare"
          src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/skinlift_with_collagen_boosting_skincare_2.jpg?v=1748162778"
        />
      </div>
      <div class="hero-side-text right-text">
        <div class="side-text-content">
          <div class="side-text-line">NO NEEDLES</div>
          <div class="side-text-line">JUST COLLAGEN</div>
          <div class="side-text-line">SCIENCE</div>
        </div>
      </div>
    </div>
  </div>
  <!-- Editorial Introduction -->
  <section class="editorial-section intro-section">
    <div class="section-grid">
      <div class="grid-column sidebar">
        <div class="sidebar-content">
          <div class="sidebar-number">01</div>
          <div class="sidebar-title">THE BREAKTHROUGH</div>
          <div class="sidebar-line"><br /></div>
        </div>
      </div>
      <div class="grid-column main-content">
        <p class="editorial-lead">
          Dermatological research reveals that skin firmness requires more than
          just collagen stimulation.
        </p>
        <p class="editorial-text">
          Our Tri-CollagenLift™ System addresses skin architecture
          comprehensively through three synergistic pathways, utilizing high
          performance actives to transform skin from within. This isn't simply
          about adding collagen—it's about rebuilding the entire structural
          matrix that supports your skin's natural beauty.
        </p>
        <div class="editorial-quote">
          "The future of skincare lies not in temporary fixes, but in supporting
          the skin's natural regenerative processes."
        </div>
        <p class="editorial-citation">
          — Journal of Dermatological Science, 2023
        </p>
        <a
          class="editorial-link"
          href="https://pmc.ncbi.nlm.nih.gov/articles/PMC11995770/"
          target="_blank"
          >View Latest Clinical Research</a
        >
      </div>
    </div>
  </section>
  <!-- The Matrix Section - Dark Background -->
  <section class="editorial-section matrix-section dark">
    <div class="section-header">
      <h2 class="section-title">THE TRI-COLLAGENLIFT™ MATRIX</h2>
      <div class="section-line"><br /></div>
    </div>
    <div class="matrix-grid">
      <div class="matrix-column">
        <div class="matrix-phase">
          <div class="phase-number">N°1</div>
          <h3 class="phase-title">ACTIVATE</h3>
          <p class="phase-description">
            Collagen renewal begins here. Cellular signaling is reawakened,
            stimulating fibroblasts to rebuild the skin's structural matrix and
            improve tone, texture, and elasticity.
          </p>
        </div>
      </div>
      <div class="matrix-column">
        <div class="matrix-phase">
          <div class="phase-number">N°2</div>
          <h3 class="phase-title">PROTECT</h3>
          <p class="phase-description">
            New collagen is vulnerable. Here, the skin forms a protective
            barrier against UV rays and environmental stressors, reducing
            collagen degradation and strengthening long-term skin resilience.
          </p>
        </div>
      </div>
      <div class="matrix-column">
        <div class="matrix-phase">
          <div class="phase-number">N°3</div>
          <h3 class="phase-title">BALANCE</h3>
          <p class="phase-description">
            Inflammation disrupts collagen health. This phase calms reactive
            skin, stabilizes the skin barrier, and maintains optimal conditions
            for collagen to function effectively.
          </p>
        </div>
      </div>
    </div>
  </section>
  <!-- Active Complex Section -->
  <section class="editorial-section complex-section">
    <div class="section-header centered">
      <h2 class="section-title">THE ACTIVE COMPLEX</h2>
      <div class="section-line"><br /></div>
    </div>
    <div class="complex-grid">
      <div class="complex-item">
        <div class="complex-number">01</div>
        <h4 class="complex-name">Hyaluronic Acid</h4>
        <p class="complex-description">
          Recent research shows hyaluronic acid activates specific cellular
          pathways that stimulate collagen synthesis.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">02</div>
        <h4 class="complex-name">Aloe Juice</h4>
        <p class="complex-description">
          Rich in polysaccharides that soothe inflammation while providing deep
          hydration and supporting skin repair.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">03</div>
        <h4 class="complex-name">Marine Actives</h4>
        <p class="complex-description">
          Derived from Nordic algae, these compounds help maintain skin
          elasticity and resilience.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">04</div>
        <h4 class="complex-name">Vitamin C</h4>
        <p class="complex-description">
          Stabilized form that stimulates collagen synthesis while providing
          powerful antioxidant protection.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">05</div>
        <h4 class="complex-name">Bakuchiol</h4>
        <p class="complex-description">
          Plant-derived retinol alternative that stimulates collagen renewal
          without irritation.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">06</div>
        <h4 class="complex-name">Nordic Berry Blend</h4>
        <p class="complex-description">
          Concentrated antioxidants from cloudberry, lingonberry and sea
          buckthorn that shield collagen from environmental damage.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">07</div>
        <h4 class="complex-name">Ceramide Complex</h4>
        <p class="complex-description">
          Strengthens skin barrier function and supports optimal moisture
          retention for enhanced firmness.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">08</div>
        <h4 class="complex-name">Advanced Peptide Complex</h4>
        <p class="complex-description">
          Revolutionary peptide technology that signals skin cells to boost
          natural collagen production and improve skin elasticity.
        </p>
      </div>
      <div class="complex-item">
        <div class="complex-number">09</div>
        <h4 class="complex-name">Botanical Stem Cells</h4>
        <p class="complex-description">
          Plant-derived stem cells that help regenerate skin tissue and promote
          cellular renewal for visibly younger-looking skin.
        </p>
      </div>
    </div>
  </section>
  <!-- Clinical Results Section -->
  <section class="editorial-section results-section dark">
    <div class="section-header centered">
      <h2 class="section-title">CLINICAL EVIDENCE</h2>
      <div class="section-line"><br /></div>
      <p class="section-subtitle">
        Test participants aged 27-50 experienced measurable improvements
      </p>
    </div>
    <div class="results-timeline">
      <div class="timeline-track"><br /></div>
      <div data-week="1" class="timeline-point">
        <div class="point-marker"><br /></div>
        <div class="point-content">
          <div class="point-week">WEEK 1</div>
          <div class="point-result">87%</div>
          <div class="point-description">
            Reported reduced skin irritation and improved comfort
          </div>
        </div>
      </div>
      <div data-week="3" class="timeline-point">
        <div class="point-marker"><br /></div>
        <div class="point-content">
          <div class="point-week">WEEK 3</div>
          <div class="point-result">92%</div>
          <div class="point-description">
            Experienced smoother and more radiant skin texture
          </div>
        </div>
      </div>
      <div data-week="8" class="timeline-point">
        <div class="point-marker"><br /></div>
        <div class="point-content">
          <div class="point-week">WEEK 8</div>
          <div class="point-result">96%</div>
          <div class="point-description">
            Showed measurably firmer skin with improved elasticity
          </div>
        </div>
      </div>
    </div>
    <div class="results-visualization">
      <div class="visualization-title">SKIN IMPROVEMENT PROGRESSION</div>
      <div class="visualization-graph">
        <div data-week="1" class="graph-column">
          <div style="height: 30%" class="column-fill">
            <span class="column-percentage">30%</span>
          </div>
          <div class="column-label">Week 1</div>
        </div>
        <div data-week="3" class="graph-column">
          <div style="height: 65%" class="column-fill">
            <span class="column-percentage">65%</span>
          </div>
          <div class="column-label">Week 3</div>
        </div>
        <div data-week="8" class="graph-column">
          <div style="height: 95%" class="column-fill">
            <span class="column-percentage">95%</span>
          </div>
          <div class="column-label">Week 8</div>
        </div>
      </div>
      <div class="visualization-caption">
        Overall skin improvement as measured by clinical assessment
      </div>
    </div>
  </section>
  <!-- Realistic Promise Section -->
  <section class="editorial-section promise-section">
    <div class="promise-content">
      <div class="promise-header">
        <h2 class="promise-title">THE CATHAM PROMISE</h2>
        <div class="promise-line"><br /></div>
      </div>
      <div class="promise-text">
        <p class="promise-lead">
          At Cathám, we believe in realistic expectations and transparent
          science.
        </p>
        <p>
          While we do not claim our products can replicate the muscle-freezing
          effects of injectable treatments—as skincare cannot and should not
          paralyze facial muscles—we can confidently state that our
          Tri-CollagenLift™ System works to restore collagen from the ground up.
        </p>
        <p>
          By supporting collagen's natural cycles and renewal processes, we
          provide a sustainable approach to skin health that works with your
          body, not against it. This is skincare that respects your skin's
          intelligence while enhancing its natural ability to regenerate and
          thrive.
        </p>
        <p>
          The result is not an artificial freeze, but a genuine restoration—skin
          that looks and feels authentically revitalized, with improved
          firmness, elasticity, and radiance that comes from within.
        </p>
      </div>
      <div class="promise-signature">
        <div class="signature-line"><br /></div>
        <div class="signature-name">Cathám</div>
        <div class="signature-title">CLEAN BEAUTY REVOLUTION</div>
      </div>
    </div>
  </section>
</div>
<style>
  /* High-End Editorial Magazine Styling */
  @import url("https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap");

  body {
    margin: 0;
    padding: 0;
    color: #111;
    background-color: #fff;
    font-family: "Fira Code", monospace;
    line-height: 1.6;
  }

  .editorial-magazine {
    max-width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }

  /* Magazine Masthead */
  .magazine-masthead {
    padding: 60px 40px;
    text-align: center;
    background-color: #000;
    color: #fff;
    position: relative;
  }

  .issue-details {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    font-family: "Fira Code", monospace;
    font-size: 12px;
    letter-spacing: 2px;
  }

  .magazine-title {
    font-family: "Old Standard TT", serif;
    font-size: 72px;
    margin: 0 0 20px;
    font-weight: 700;
    letter-spacing: 4px;
    line-height: 1;
    color: #fff;
  }

  .magazine-subtitle {
    font-family: "Fira Code", monospace;
    font-size: 14px;
    letter-spacing: 3px;
    font-weight: 700;
  }

  /* Hero Section */
  .hero-section {
    padding: 60px 20px;
    background-color: #000;
  }

  .hero-container {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1400px;
    margin: 0 auto;
    gap: 20px;
  }

  .hero-side-text {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .side-text-content {
    text-align: center;
  }

  .side-text-line {
    font-family: "Old Standard TT", serif;
    font-size: 22px;
    font-weight: 700;
    line-height: 1.2;
    color: #fff;
    margin-bottom: 8px;
    letter-spacing: 2px;
  }

  .hero-image-container {
    flex: 0 0 60%;
    display: flex;
    justify-content: center;
  }

  .hero-image {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  /* Editorial Sections */
  .editorial-section {
    padding: 100px 40px;
  }

  .editorial-section.dark {
    background-color: #000;
    color: #fff;
  }

  .section-grid {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 60px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .sidebar-content {
    position: sticky;
    top: 100px;
  }

  .sidebar-number {
    font-family: "Fira Code", monospace;
    font-size: 14px;
    margin-bottom: 10px;
  }

  .sidebar-title {
    font-family: "Fira Code", monospace;
    font-size: 14px;
    letter-spacing: 2px;
    margin-bottom: 15px;
    font-weight: 700;
  }

  .sidebar-line {
    width: 40px;
    height: 1px;
    background-color: currentColor;
  }

  .editorial-lead {
    font-size: 28px;
    line-height: 1.4;
    margin-bottom: 30px;
    font-weight: 400;
  }

  .editorial-text {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 800px;
  }

  .editorial-quote {
    font-size: 24px;
    font-style: italic;
    line-height: 1.4;
    margin: 40px 0 15px;
    padding-left: 30px;
    border-left: 2px solid currentColor;
    max-width: 700px;
  }

  .editorial-citation {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    margin-bottom: 40px;
    padding-left: 30px;
  }

  .editorial-link {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: inherit;
    text-decoration: none;
    border-bottom: 1px solid;
    padding-bottom: 2px;
    transition: opacity 0.3s ease;
    font-weight: 700;
  }

  .editorial-link:hover {
    opacity: 0.7;
  }

  /* Section Headers */
  .section-header {
    margin-bottom: 60px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
  }

  .section-header.centered {
    text-align: center;
  }

  .section-title {
    font-family: "Old Standard TT", serif;
    font-size: 36px;
    letter-spacing: 2px;
    font-weight: 700;
    margin: 0 0 20px;
    text-transform: uppercase;
  }

  /* Title color adjustments for different backgrounds */
  .editorial-section:not(.dark) .section-title {
    color: #000;
  }

  .editorial-section.dark .section-title {
    color: #fff;
  }

  .section-line {
    width: 60px;
    height: 1px;
    background-color: currentColor;
    margin: 0 0 20px;
  }

  .section-header.centered .section-line {
    margin-left: auto;
    margin-right: auto;
  }

  .section-subtitle {
    font-size: 18px;
    font-style: italic;
    max-width: 600px;
    margin: 0 auto;
  }

  /* Matrix Section */
  .matrix-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .matrix-phase {
    padding: 40px;
    background-color: rgba(255, 255, 255, 0.05);
    height: 100%;
  }

  .phase-number {
    font-family: "Old Standard TT", serif;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
  }

  .phase-title {
    font-family: "Old Standard TT", serif;
    font-size: 24px;
    letter-spacing: 2px;
    margin: 0 0 30px;
    font-weight: 700;
    color: #fff;
  }

  .phase-description {
    font-size: 14px;
    line-height: 1.6;
  }

  /* Complex Section */
  .complex-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .complex-item {
    padding: 30px;
    background-color: #f9f9f9;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .complex-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .complex-number {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    margin-bottom: 15px;
    color: #999;
  }

  .complex-name {
    font-family: "Old Standard TT", serif;
    font-size: 22px;
    margin: 0 0 15px;
    font-weight: 700;
    color: #000;
  }

  .complex-description {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
  }

  /* Results Section */
  .results-timeline {
    position: relative;
    max-width: 1000px;
    margin: 0 auto 80px;
    padding: 60px 0;
  }

  .timeline-track {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%);
  }

  .timeline-point {
    position: absolute;
    top: 0;
    width: 200px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s forwards;
    animation-delay: calc(var(--week) * 0.3s);
  }

  .timeline-point[data-week="1"] {
    left: 0%;
    --week: 1;
  }
  .timeline-point[data-week="3"] {
    left: 40%;
    --week: 2;
  }
  .timeline-point[data-week="8"] {
    left: 80%;
    --week: 3;
  }

  .point-marker {
    width: 12px;
    height: 12px;
    background-color: #fff;
    border-radius: 50%;
    margin-bottom: 20px;
  }

  .point-week {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    letter-spacing: 1px;
    margin-bottom: 10px;
    font-weight: 700;
  }

  .point-result {
    font-size: 48px;
    line-height: 1;
    margin-bottom: 15px;
  }

  .point-description {
    font-size: 16px;
    max-width: 180px;
  }

  .results-visualization {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
  }

  .visualization-title {
    font-family: "Fira Code", monospace;
    font-size: 14px;
    letter-spacing: 2px;
    margin-bottom: 20px;
    font-weight: 700;
  }

  .visualization-graph {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    height: 300px;
    margin-bottom: 20px;
  }

  .graph-column {
    width: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .column-fill {
    width: 100%;
    background-color: #fff;
    position: relative;
    opacity: 0;
    animation: growUp 1.5s forwards;
    animation-delay: calc(var(--week) * 0.3s);
  }

  .graph-column[data-week="1"] {
    --week: 1;
  }
  .graph-column[data-week="3"] {
    --week: 2;
  }
  .graph-column[data-week="8"] {
    --week: 3;
  }

  .column-percentage {
    position: absolute;
    top: -25px;
    left: 0;
    right: 0;
    text-align: center;
    font-family: "Fira Code", monospace;
    font-size: 14px;
    font-weight: 700;
  }

  .column-label {
    margin-top: 15px;
    font-family: "Fira Code", monospace;
    font-size: 14px;
  }

  .visualization-caption {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }

  /* Promise Section */
  .promise-section {
    background-color: #f9f9f9;
  }

  .promise-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .promise-header {
    text-align: center;
    margin-bottom: 60px;
  }

  .promise-title {
    font-family: "Old Standard TT", serif;
    font-size: 36px;
    letter-spacing: 2px;
    font-weight: 700;
    margin: 0 0 20px;
    text-transform: uppercase;
    color: #000;
  }

  .promise-line {
    width: 60px;
    height: 1px;
    background-color: #000;
    margin: 0 auto;
  }

  .promise-lead {
    font-size: 24px;
    line-height: 1.4;
    margin-bottom: 30px;
  }

  .promise-text p {
    font-size: 18px;
    margin-bottom: 25px;
    line-height: 1.7;
  }

  .promise-signature {
    margin-top: 60px;
    text-align: center;
  }

  .signature-line {
    width: 40px;
    height: 1px;
    background-color: #000;
    margin: 0 auto 20px;
  }

  .signature-name {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .signature-title {
    font-family: "Fira Code", monospace;
    font-size: 10px;
    letter-spacing: 2px;
    font-weight: 700;
  }

  /* Animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes growUp {
    from {
      opacity: 0;
      height: 0;
    }
    to {
      opacity: 1;
      height: 100%;
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 1200px) {
    .section-grid {
      grid-template-columns: 1fr 2fr;
    }

    .matrix-grid {
      grid-template-columns: 1fr;
      gap: 30px;
    }

    .timeline-point[data-week="1"] {
      left: 0%;
    }
    .timeline-point[data-week="3"] {
      left: 35%;
    }
    .timeline-point[data-week="8"] {
      left: 70%;
    }

    /* Hero section adjustments for tablets */
    .side-text-line {
      font-size: 20px;
    }
  }

  @media (max-width: 992px) {
    /* Hide side texts on smaller tablets and mobile */
    .hero-container {
      flex-direction: column;
      gap: 0;
    }

    .hero-side-text {
      display: none;
    }

    .hero-image-container {
      flex: 1;
      width: 100%;
    }

    .hero-section {
      padding: 40px 20px;
    }
  }

  @media (max-width: 768px) {
    .magazine-masthead {
      padding: 40px 20px;
    }

    .magazine-title {
      font-size: 48px;
    }

    .editorial-section {
      padding: 60px 20px;
    }

    /* Hide side texts on mobile and make image full width */
    .hero-container {
      flex-direction: column;
      gap: 0;
    }

    .hero-side-text {
      display: none;
    }

    .hero-image-container {
      flex: 1;
      width: 100%;
    }

    .hero-section {
      padding: 40px 20px;
    }

    .section-grid {
      grid-template-columns: 1fr;
      gap: 40px;
    }

    .sidebar-content {
      position: static;
      margin-bottom: 30px;
    }

    .editorial-lead {
      font-size: 24px;
    }

    .editorial-text {
      font-size: 16px;
    }

    .editorial-quote {
      font-size: 20px;
      padding-left: 20px;
    }

    .section-title {
      font-size: 28px;
    }

    .complex-grid {
      grid-template-columns: 1fr;
    }

    .results-timeline {
      padding: 40px 0;
      display: flex;
      flex-direction: column;
      gap: 40px;
    }

    .timeline-track {
      display: none;
    }

    .timeline-point {
      position: static;
      width: 100%;
      opacity: 1;
      transform: none;
      animation: none;
      text-align: center;
      padding: 20px;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
    }

    .point-marker {
      margin: 0 auto 20px;
    }

    .point-result {
      font-size: 36px;
    }

    .point-description {
      max-width: 100%;
      margin: 0 auto;
    }

    .visualization-graph {
      height: 200px;
    }

    .graph-column {
      width: 60px;
    }

    .promise-title {
      font-size: 28px;
    }

    .promise-lead {
      font-size: 20px;
    }

    .promise-text p {
      font-size: 16px;
    }
  }
</style>
<p>&nbsp;</p>
