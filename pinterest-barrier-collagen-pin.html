<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Pinterest Pin: Before Collagen Can Build, Your Barrier Must Hold
    </title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="pinterest-pin">
      <!-- Header with Hook -->
      <div class="pin-header">
        <div class="hook-text">STOP</div>
        <div class="sub-hook">Before You Buy Another Collagen Serum</div>
      </div>

      <!-- Main Visual Content -->
      <div class="pin-content">
        <div class="main-title">
          Before Collagen Can Build,<br />
          <span class="highlight">Your Barrier Must Hold</span>
        </div>

        <!-- Key Stats Section -->
        <div class="stats-section">
          <div class="stat-item">
            <div class="stat-number">40%</div>
            <div class="stat-text">
              Lower collagen synthesis in compromised barriers
            </div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <div class="stat-number">2-3</div>
            <div class="stat-text">
              Years barrier dysfunction precedes visible aging
            </div>
          </div>
        </div>

        <!-- Problem Statement -->
        <div class="problem-section">
          <div class="problem-title">The Problem:</div>
          <div class="problem-text">
            Without a fortified barrier, even the most sophisticated anti-aging
            actives are rendered ineffective—like pouring water through a sieve.
          </div>
        </div>

        <!-- Solution Steps -->
        <div class="solution-section">
          <div class="solution-title">The Fix:</div>
          <div class="solution-steps">
            <div class="step">
              <span class="step-number">1</span>
              <span class="step-text">Repair barrier with ceramides</span>
            </div>
            <div class="step">
              <span class="step-number">2</span>
              <span class="step-text">Reduce inflammation</span>
            </div>
            <div class="step">
              <span class="step-number">3</span>
              <span class="step-text">THEN stimulate collagen</span>
            </div>
          </div>
        </div>

        <!-- Key Ingredients -->
        <div class="ingredients-section">
          <div class="ingredients-title">Barrier Heroes:</div>
          <div class="ingredients-grid">
            <div class="ingredient">Ceramides</div>
            <div class="ingredient">Cholesterol</div>
            <div class="ingredient">Fatty Acids</div>
            <div class="ingredient">Antioxidants</div>
          </div>
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
          <div class="cta-text">Save this for your next skincare haul!</div>
          <div class="cta-arrow">↓</div>
        </div>
      </div>

      <!-- Footer -->
      <div class="pin-footer">
        <div class="brand-section">
          <div class="brand-name">CATHÁM</div>
          <div class="brand-tagline">Science-Backed Nordic Skincare</div>
        </div>
        <div class="pin-type">SKINCARE SCIENCE</div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Fira Code", monospace;
        background-color: #f0f0f0;
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
      }

      .pinterest-pin {
        width: 600px;
        height: 900px;
        background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
        color: #ffffff;
        position: relative;
        overflow: hidden;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }

      /* Header Section */
      .pin-header {
        padding: 40px 30px 20px;
        text-align: center;
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        position: relative;
      }

      .hook-text {
        font-family: "Old Standard TT", serif;
        font-size: 48px;
        font-weight: 700;
        letter-spacing: 4px;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .sub-hook {
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 1px;
        opacity: 0.9;
      }

      /* Main Content */
      .pin-content {
        padding: 40px 30px;
      }

      .main-title {
        font-family: "Old Standard TT", serif;
        font-size: 36px;
        font-weight: 700;
        line-height: 1.2;
        text-align: center;
        margin-bottom: 40px;
        letter-spacing: 1px;
      }

      .highlight {
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      /* Stats Section */
      .stats-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 40px;
        padding: 25px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .stat-item {
        text-align: center;
        flex: 1;
      }

      .stat-number {
        font-family: "Old Standard TT", serif;
        font-size: 42px;
        font-weight: 700;
        color: #ff6b6b;
        line-height: 1;
        margin-bottom: 8px;
      }

      .stat-text {
        font-size: 12px;
        line-height: 1.3;
        opacity: 0.9;
      }

      .stat-divider {
        width: 1px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        margin: 0 20px;
      }

      /* Problem Section */
      .problem-section {
        margin-bottom: 35px;
        padding: 25px;
        background: rgba(255, 107, 107, 0.1);
        border-left: 4px solid #ff6b6b;
        border-radius: 0 10px 10px 0;
      }

      .problem-title {
        font-family: "Old Standard TT", serif;
        font-size: 20px;
        font-weight: 700;
        color: #ff6b6b;
        margin-bottom: 12px;
        letter-spacing: 1px;
      }

      .problem-text {
        font-size: 14px;
        line-height: 1.5;
        font-style: italic;
      }

      /* Solution Section */
      .solution-section {
        margin-bottom: 35px;
      }

      .solution-title {
        font-family: "Old Standard TT", serif;
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 20px;
        letter-spacing: 1px;
        color: #4ecdc4;
      }

      .solution-steps {
        space-y: 15px;
      }

      .step {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 15px;
        background: rgba(78, 205, 196, 0.1);
        border-radius: 10px;
        border: 1px solid rgba(78, 205, 196, 0.2);
      }

      .step-number {
        width: 30px;
        height: 30px;
        background: #4ecdc4;
        color: #000;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        margin-right: 15px;
        font-size: 14px;
      }

      .step-text {
        font-size: 14px;
        font-weight: 600;
      }

      /* Ingredients Section */
      .ingredients-section {
        margin-bottom: 35px;
      }

      .ingredients-title {
        font-family: "Old Standard TT", serif;
        font-size: 18px;
        font-weight: 700;
        margin-bottom: 15px;
        letter-spacing: 1px;
      }

      .ingredients-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
      }

      .ingredient {
        padding: 12px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        text-align: center;
        font-size: 12px;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* CTA Section */
      .cta-section {
        text-align: center;
        margin-bottom: 20px;
      }

      .cta-text {
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 10px;
        color: #ffd93d;
      }

      .cta-arrow {
        font-size: 24px;
        color: #ffd93d;
        animation: bounce 2s infinite;
      }

      @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-10px);
        }
        60% {
          transform: translateY(-5px);
        }
      }

      /* Footer */
      .pin-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 25px 30px;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      .brand-name {
        font-family: "Old Standard TT", serif;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: 2px;
      }

      .brand-tagline {
        font-size: 10px;
        opacity: 0.8;
        letter-spacing: 1px;
      }

      .pin-type {
        font-size: 10px;
        font-weight: 600;
        letter-spacing: 1px;
        padding: 5px 10px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Responsive adjustments for actual Pinterest pin size */
      @media (max-width: 400px) {
        .pinterest-pin {
          width: 300px;
          height: 450px;
        }

        .hook-text {
          font-size: 32px;
        }

        .main-title {
          font-size: 24px;
        }

        .stat-number {
          font-size: 28px;
        }
      }
    </style>
  </body>
</html>
