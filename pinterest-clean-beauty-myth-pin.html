<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pinterest Pin: Clean Beauty Myth - Cathám New York</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="pinterest-pin">
      <!-- Header with Hook -->
      <div class="pin-header">
        <div class="hook-text">MYTH</div>
        <div class="sub-hook">"Clean Beauty" Means the Same Thing Everywhere</div>
      </div>

      <!-- Main Content -->
      <div class="pin-content">
        <div class="main-title">
          The Truth About<br />
          <span class="highlight">Global Beauty Standards</span>
        </div>

        <!-- Comparison Section -->
        <div class="comparison-section">
          <div class="comparison-item us">
            <div class="country-flag">🇺🇸</div>
            <div class="country-name">United States</div>
            <div class="banned-count">30</div>
            <div class="banned-text">banned ingredients</div>
          </div>
          
          <div class="vs-divider">VS</div>
          
          <div class="comparison-item eu">
            <div class="country-flag">🇪🇺</div>
            <div class="country-name">European Union</div>
            <div class="banned-count">1,600+</div>
            <div class="banned-text">banned ingredients</div>
          </div>
        </div>

        <!-- Reality Check -->
        <div class="reality-section">
          <div class="reality-title">The Reality:</div>
          <div class="reality-text">
            Your "clean" US brand might use ingredients<br>
            that have been banned in Europe for years.
          </div>
        </div>

        <!-- Cathám Standard -->
        <div class="standard-section">
          <div class="standard-title">The Cathám Standard:</div>
          <div class="standard-list">
            <div class="standard-item">✓ European regulatory compliance</div>
            <div class="standard-item">✓ Northern European botanical sourcing</div>
            <div class="standard-item">✓ NYC formulation expertise</div>
          </div>
        </div>

        <!-- CTA -->
        <div class="cta-section">
          <div class="cta-main">Ready for real clean beauty standards?</div>
          <div class="cta-button">
            <span class="cta-text">Follow for European beauty insights</span>
            <span class="cta-arrow">→</span>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="pin-footer">
        <div class="brand-section">
          <div class="brand-name">CATHÁM</div>
          <div class="brand-location">NEW YORK</div>
        </div>
        <div class="pin-type">BEAUTY STANDARDS</div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Fira Code', monospace;
        background: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .pinterest-pin {
        width: 400px;
        height: 600px;
        background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }

      /* Header */
      .pin-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 25px 20px;
        text-align: center;
      }

      .hook-text {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 42px;
        letter-spacing: 3px;
        margin-bottom: 8px;
      }

      .sub-hook {
        font-family: 'Fira Code', monospace;
        font-size: 14px;
        font-weight: 400;
        opacity: 0.95;
        letter-spacing: 0.5px;
        line-height: 1.3;
      }

      /* Main Content */
      .pin-content {
        flex: 1;
        padding: 25px 20px;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .main-title {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 28px;
        line-height: 1.2;
        text-align: center;
        color: #1a1a1a;
      }

      .highlight {
        color: #dc3545;
        position: relative;
      }

      .highlight::after {
        content: '';
        position: absolute;
        bottom: 2px;
        left: 0;
        right: 0;
        height: 3px;
        background: #dc3545;
        opacity: 0.3;
      }

      /* Comparison Section */
      .comparison-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        border: 2px solid #e9ecef;
      }

      .comparison-item {
        text-align: center;
        flex: 1;
      }

      .country-flag {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .country-name {
        font-family: 'Fira Code', monospace;
        font-size: 10px;
        color: #666;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .banned-count {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 32px;
        color: #1a1a1a;
        line-height: 1;
        margin-bottom: 5px;
      }

      .eu .banned-count {
        color: #28a745;
      }

      .banned-text {
        font-family: 'Fira Code', monospace;
        font-size: 11px;
        color: #666;
      }

      .vs-divider {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 18px;
        color: #dc3545;
        margin: 0 15px;
      }

      /* Reality Section */
      .reality-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 18px;
        border-radius: 12px;
        text-align: center;
      }

      .reality-title {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 16px;
        color: #856404;
        margin-bottom: 10px;
      }

      .reality-text {
        font-family: 'Fira Code', monospace;
        font-size: 12px;
        color: #856404;
        line-height: 1.4;
      }

      /* Standard Section */
      .standard-section {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        padding: 18px;
        border-radius: 12px;
      }

      .standard-title {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 16px;
        color: #155724;
        margin-bottom: 12px;
        text-align: center;
      }

      .standard-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .standard-item {
        font-family: 'Fira Code', monospace;
        font-size: 12px;
        color: #155724;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      /* CTA Section */
      .cta-section {
        text-align: center;
        margin-top: auto;
      }

      .cta-main {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 18px;
        color: #1a1a1a;
        margin-bottom: 15px;
      }

      .cta-button {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: white;
        padding: 12px 18px;
        border-radius: 25px;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        transition: transform 0.2s ease;
      }

      .cta-button:hover {
        transform: translateY(-2px);
      }

      .cta-text {
        font-family: 'Fira Code', monospace;
        font-size: 12px;
        font-weight: 600;
      }

      .cta-arrow {
        font-size: 16px;
        font-weight: bold;
      }

      /* Footer */
      .pin-footer {
        background: #1a1a1a;
        color: white;
        padding: 18px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .brand-section {
        text-align: left;
      }

      .brand-name {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 20px;
        letter-spacing: 2px;
        margin-bottom: 3px;
      }

      .brand-location {
        font-family: 'Fira Code', monospace;
        font-size: 11px;
        opacity: 0.8;
        letter-spacing: 1px;
      }

      .pin-type {
        font-family: 'Fira Code', monospace;
        font-size: 9px;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 1px;
        writing-mode: vertical-rl;
        text-orientation: mixed;
      }

      /* Responsive */
      @media (max-width: 450px) {
        .pinterest-pin {
          width: 350px;
          height: 525px;
        }

        .hook-text {
          font-size: 36px;
        }

        .main-title {
          font-size: 24px;
        }

        .banned-count {
          font-size: 28px;
        }
      }
    </style>
  </body>
</html>
