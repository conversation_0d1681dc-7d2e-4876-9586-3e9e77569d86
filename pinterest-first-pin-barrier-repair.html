<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Pinterest Pin: Your $200 Serums Aren't Working - Cathám New York
    </title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="pinterest-pin">
      <!-- Header with Hook -->
      <div class="pin-header">
        <div class="hook-text">STOP</div>
        <div class="sub-hook">Your $200 Serums Aren't Working</div>
      </div>

      <!-- Main Visual Content -->
      <div class="pin-content">
        <div class="main-title">
          Fix Your Barrier First,<br />
          <span class="highlight">Then Watch Everything Work</span>
        </div>

        <!-- Problem Section -->
        <div class="problem-section">
          <div class="problem-title">The Problem:</div>
          <div class="problem-visual">
            <div class="broken-barrier">
              <div class="barrier-text">Compromised Barrier</div>
              <div class="cracks">⚡ ⚡ ⚡</div>
            </div>
            <div class="arrow-down">↓</div>
            <div class="wasted-products">
              <div class="product-icons">💧 💧 💧</div>
              <div class="waste-text">Expensive actives going to waste</div>
            </div>
          </div>
        </div>

        <!-- Key Stats Section -->
        <div class="stats-section">
          <div class="stat-item">
            <div class="stat-number">40%</div>
            <div class="stat-text">Lower absorption in damaged barriers</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <div class="stat-number">2-3</div>
            <div class="stat-text">
              Years barrier damage precedes visible aging
            </div>
          </div>
        </div>

        <!-- Solution Steps -->
        <div class="solution-section">
          <div class="solution-title">The Cathám Solution:</div>
          <div class="solution-steps">
            <div class="step">
              <span class="step-number">1</span>
              <span class="step-text">Repair with European ceramides</span>
            </div>
            <div class="step">
              <span class="step-number">2</span>
              <span class="step-text">Strengthen with botanical actives</span>
            </div>
            <div class="step">
              <span class="step-number">3</span>
              <span class="step-text">THEN layer your expensive serums</span>
            </div>
          </div>
        </div>

        <!-- Key Ingredients -->
        <div class="ingredients-section">
          <div class="ingredients-title">European Barrier Heroes:</div>
          <div class="ingredients-grid">
            <div class="ingredient">Ceramides</div>
            <div class="ingredient">Sea Buckthorn</div>
            <div class="ingredient">Cloudberry</div>
            <div class="ingredient">Marine Actives</div>
          </div>
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
          <div class="cta-text">Save this before your next skincare haul!</div>
          <div class="cta-arrow">↓</div>
        </div>
      </div>

      <!-- Footer -->
      <div class="pin-footer">
        <div class="brand-section">
          <div class="brand-name">CATHÁM</div>
          <div class="brand-location">NEW YORK</div>
          <div class="brand-tagline">Premium European Botanicals</div>
        </div>
        <div class="pin-type">SKINCARE SCIENCE</div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Fira Code", monospace;
        background: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .pinterest-pin {
        width: 400px;
        height: 600px;
        background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
        display: flex;
        flex-direction: column;
      }

      /* Header Section */
      .pin-header {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: white;
        padding: 20px;
        text-align: center;
        position: relative;
      }

      .hook-text {
        font-family: "Old Standard TT", serif;
        font-weight: 700;
        font-size: 36px;
        letter-spacing: 2px;
        margin-bottom: 5px;
      }

      .sub-hook {
        font-family: "Fira Code", monospace;
        font-size: 14px;
        font-weight: 400;
        opacity: 0.9;
        letter-spacing: 0.5px;
      }

      /* Main Content */
      .pin-content {
        flex: 1;
        padding: 25px 20px;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .main-title {
        font-family: "Old Standard TT", serif;
        font-weight: 700;
        font-size: 28px;
        line-height: 1.2;
        text-align: center;
        color: #1a1a1a;
      }

      .highlight {
        color: #2d2d2d;
        position: relative;
      }

      .highlight::after {
        content: "";
        position: absolute;
        bottom: 2px;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #1a1a1a, #2d2d2d);
        opacity: 0.3;
      }

      /* Problem Section */
      .problem-section {
        background: #f8f8f8;
        padding: 15px;
        border-radius: 12px;
        border-left: 4px solid #1a1a1a;
      }

      .problem-title {
        font-family: "Fira Code", monospace;
        font-weight: 600;
        font-size: 12px;
        color: #1a1a1a;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .problem-visual {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
      }

      .broken-barrier {
        text-align: center;
      }

      .barrier-text {
        font-family: "Fira Code", monospace;
        font-size: 11px;
        color: #666;
        margin-bottom: 5px;
      }

      .cracks {
        font-size: 16px;
        color: #ff6b6b;
      }

      .arrow-down {
        font-size: 20px;
        color: #1a1a1a;
      }

      .wasted-products {
        text-align: center;
      }

      .product-icons {
        font-size: 14px;
        margin-bottom: 5px;
        opacity: 0.7;
      }

      .waste-text {
        font-family: "Fira Code", monospace;
        font-size: 10px;
        color: #666;
      }

      /* Stats Section */
      .stats-section {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background: #1a1a1a;
        color: white;
        padding: 15px;
        border-radius: 12px;
      }

      .stat-item {
        text-align: center;
        flex: 1;
      }

      .stat-number {
        font-family: "Old Standard TT", serif;
        font-weight: 700;
        font-size: 32px;
        line-height: 1;
        margin-bottom: 5px;
      }

      .stat-text {
        font-family: "Fira Code", monospace;
        font-size: 10px;
        line-height: 1.3;
        opacity: 0.9;
      }

      .stat-divider {
        width: 1px;
        height: 40px;
        background: rgba(255, 255, 255, 0.3);
        margin: 0 10px;
      }

      /* Solution Section */
      .solution-section {
        background: #f0f0f0;
        padding: 15px;
        border-radius: 12px;
      }

      .solution-title {
        font-family: "Fira Code", monospace;
        font-weight: 600;
        font-size: 12px;
        color: #1a1a1a;
        margin-bottom: 12px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .solution-steps {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .step {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .step-number {
        background: #1a1a1a;
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Fira Code", monospace;
        font-size: 10px;
        font-weight: 600;
        flex-shrink: 0;
      }

      .step-text {
        font-family: "Fira Code", monospace;
        font-size: 11px;
        color: #333;
        line-height: 1.3;
      }

      /* Ingredients Section */
      .ingredients-section {
        text-align: center;
      }

      .ingredients-title {
        font-family: "Fira Code", monospace;
        font-weight: 600;
        font-size: 11px;
        color: #1a1a1a;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .ingredients-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
      }

      .ingredient {
        background: white;
        border: 1px solid #e0e0e0;
        padding: 8px;
        border-radius: 8px;
        font-family: "Fira Code", monospace;
        font-size: 10px;
        color: #333;
        font-weight: 500;
      }

      /* CTA Section */
      .cta-section {
        text-align: center;
        margin-top: auto;
      }

      .cta-text {
        font-family: "Fira Code", monospace;
        font-size: 12px;
        color: #1a1a1a;
        font-weight: 600;
        margin-bottom: 5px;
      }

      .cta-arrow {
        font-size: 20px;
        color: #1a1a1a;
      }

      /* Footer */
      .pin-footer {
        background: #1a1a1a;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .brand-section {
        text-align: left;
      }

      .brand-name {
        font-family: "Old Standard TT", serif;
        font-weight: 700;
        font-size: 18px;
        letter-spacing: 2px;
        margin-bottom: 2px;
      }

      .brand-location {
        font-family: "Fira Code", monospace;
        font-size: 10px;
        opacity: 0.8;
        letter-spacing: 1px;
        margin-bottom: 2px;
      }

      .brand-tagline {
        font-family: "Fira Code", monospace;
        font-size: 9px;
        opacity: 0.7;
        line-height: 1.2;
      }

      .pin-type {
        font-family: "Fira Code", monospace;
        font-size: 9px;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 1px;
        writing-mode: vertical-rl;
        text-orientation: mixed;
      }

      /* Responsive adjustments */
      @media (max-width: 450px) {
        .pinterest-pin {
          width: 350px;
          height: 525px;
        }

        .hook-text {
          font-size: 32px;
        }

        .main-title {
          font-size: 24px;
        }

        .stat-number {
          font-size: 28px;
        }
      }
    </style>
  </body>
</html>
