<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pinterest Pin: Your $200 Serums Aren't Working - Cathám New York</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="pinterest-pin">
      <!-- Header with Hook -->
      <div class="pin-header">
        <div class="hook-text">STOP</div>
        <div class="sub-hook">Your $200 Serums Aren't Working</div>
      </div>

      <!-- Main Content -->
      <div class="pin-content">
        <div class="main-title">
          Fix Your Barrier First,<br />
          <span class="highlight">Then Watch Everything Work</span>
        </div>

        <!-- Key Stat -->
        <div class="key-stat">
          <div class="stat-number">40%</div>
          <div class="stat-text">Lower absorption in damaged barriers</div>
        </div>

        <!-- The Problem -->
        <div class="problem-section">
          <div class="problem-icon">⚡</div>
          <div class="problem-text">
            <strong>Broken barrier = wasted money</strong><br>
            Your expensive serums can't penetrate damaged skin
          </div>
        </div>

        <!-- The Solution -->
        <div class="solution-section">
          <div class="solution-title">The Cathám Way:</div>
          <div class="solution-list">
            <div class="solution-item">✓ European ceramides repair</div>
            <div class="solution-item">✓ Botanical actives strengthen</div>
            <div class="solution-item">✓ Your serums finally work</div>
          </div>
        </div>

        <!-- Strong CTA -->
        <div class="cta-section">
          <div class="cta-main">Want our barrier repair secrets?</div>
          <div class="cta-button">
            <span class="cta-text">Follow for European skincare science</span>
            <span class="cta-arrow">→</span>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="pin-footer">
        <div class="brand-section">
          <div class="brand-name">CATHÁM</div>
          <div class="brand-location">NEW YORK</div>
        </div>
        <div class="pin-type">SKINCARE SCIENCE</div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Fira Code', monospace;
        background: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .pinterest-pin {
        width: 400px;
        height: 600px;
        background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }

      /* Header */
      .pin-header {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: white;
        padding: 25px 20px;
        text-align: center;
      }

      .hook-text {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 42px;
        letter-spacing: 3px;
        margin-bottom: 8px;
      }

      .sub-hook {
        font-family: 'Fira Code', monospace;
        font-size: 16px;
        font-weight: 400;
        opacity: 0.9;
        letter-spacing: 0.5px;
      }

      /* Main Content */
      .pin-content {
        flex: 1;
        padding: 30px 25px;
        display: flex;
        flex-direction: column;
        gap: 25px;
      }

      .main-title {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 32px;
        line-height: 1.2;
        text-align: center;
        color: #1a1a1a;
      }

      .highlight {
        color: #2d2d2d;
        position: relative;
      }

      .highlight::after {
        content: '';
        position: absolute;
        bottom: 3px;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #1a1a1a, #2d2d2d);
        opacity: 0.3;
      }

      /* Key Stat */
      .key-stat {
        background: #1a1a1a;
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
      }

      .stat-number {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 48px;
        line-height: 1;
        margin-bottom: 8px;
      }

      .stat-text {
        font-family: 'Fira Code', monospace;
        font-size: 14px;
        opacity: 0.9;
      }

      /* Problem Section */
      .problem-section {
        background: #f8f8f8;
        padding: 20px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 15px;
        border-left: 4px solid #ff6b6b;
      }

      .problem-icon {
        font-size: 32px;
        color: #ff6b6b;
      }

      .problem-text {
        font-family: 'Fira Code', monospace;
        font-size: 14px;
        line-height: 1.4;
        color: #333;
      }

      /* Solution Section */
      .solution-section {
        background: #f0f0f0;
        padding: 20px;
        border-radius: 12px;
      }

      .solution-title {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 18px;
        color: #1a1a1a;
        margin-bottom: 15px;
        text-align: center;
      }

      .solution-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .solution-item {
        font-family: 'Fira Code', monospace;
        font-size: 14px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      /* CTA Section */
      .cta-section {
        text-align: center;
        margin-top: auto;
      }

      .cta-main {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 20px;
        color: #1a1a1a;
        margin-bottom: 15px;
      }

      .cta-button {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 25px;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        transition: transform 0.2s ease;
      }

      .cta-button:hover {
        transform: translateY(-2px);
      }

      .cta-text {
        font-family: 'Fira Code', monospace;
        font-size: 14px;
        font-weight: 600;
      }

      .cta-arrow {
        font-size: 18px;
        font-weight: bold;
      }

      /* Footer */
      .pin-footer {
        background: #1a1a1a;
        color: white;
        padding: 20px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .brand-section {
        text-align: left;
      }

      .brand-name {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 22px;
        letter-spacing: 2px;
        margin-bottom: 3px;
      }

      .brand-location {
        font-family: 'Fira Code', monospace;
        font-size: 12px;
        opacity: 0.8;
        letter-spacing: 1px;
      }

      .pin-type {
        font-family: 'Fira Code', monospace;
        font-size: 10px;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 1px;
        writing-mode: vertical-rl;
        text-orientation: mixed;
      }

      /* Responsive */
      @media (max-width: 450px) {
        .pinterest-pin {
          width: 350px;
          height: 525px;
        }

        .hook-text {
          font-size: 36px;
        }

        .main-title {
          font-size: 28px;
        }

        .stat-number {
          font-size: 42px;
        }
      }
    </style>
  </body>
</html>
