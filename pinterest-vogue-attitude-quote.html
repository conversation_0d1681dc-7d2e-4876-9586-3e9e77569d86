<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pinterest Pin: Vogue Attitude Quote - Cathám New York</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="pinterest-pin">
      <!-- Quote Section -->
      <div class="quote-section">
        <div class="quote-mark">"</div>
        <div class="quote-text">
          We don't follow<br>
          beauty trends.<br>
          <span class="emphasis">We set standards.</span>
        </div>
        <div class="quote-mark closing">"</div>
      </div>

      <!-- Editorial Attribution -->
      <div class="attribution-section">
        <div class="attribution-line"></div>
        <div class="attribution-text">
          European botanicals.<br>
          New York attitude.<br>
          Uncompromising results.
        </div>
        <div class="attribution-line"></div>
      </div>

      <!-- Sophisticated CTA -->
      <div class="cta-section">
        <div class="cta-text">For those who demand more</div>
        <div class="cta-subtext">than marketing promises</div>
      </div>

      <!-- Brand Footer -->
      <div class="brand-footer">
        <div class="brand-main">
          <div class="brand-name">CATHÁM</div>
          <div class="brand-location">NEW YORK</div>
        </div>
        <div class="brand-philosophy">
          <div class="philosophy-text">STANDARDS</div>
          <div class="philosophy-subtext">NOT TRENDS</div>
        </div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Old Standard TT', serif;
        background: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .pinterest-pin {
        width: 400px;
        height: 600px;
        background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
        color: white;
        border-radius: 0;
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
      }

      /* Quote Section */
      .quote-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 60px 40px;
        text-align: center;
        position: relative;
      }

      .quote-mark {
        font-family: 'Old Standard TT', serif;
        font-size: 140px;
        font-weight: 400;
        color: white;
        line-height: 0.8;
        position: absolute;
        opacity: 0.08;
      }

      .quote-mark {
        top: 10px;
        left: 20px;
      }

      .quote-mark.closing {
        bottom: 160px;
        right: 20px;
        transform: rotate(180deg);
      }

      .quote-text {
        font-family: 'Old Standard TT', serif;
        font-size: 36px;
        font-weight: 400;
        line-height: 1.2;
        color: white;
        font-style: italic;
        z-index: 2;
        position: relative;
        letter-spacing: 0.5px;
      }

      .emphasis {
        font-weight: 700;
        font-style: normal;
        position: relative;
        color: #ffffff;
      }

      .emphasis::after {
        content: '';
        position: absolute;
        bottom: 3px;
        left: 0;
        right: 0;
        height: 3px;
        background: white;
        opacity: 0.4;
      }

      /* Attribution Section */
      .attribution-section {
        padding: 25px 40px;
        text-align: center;
        background: rgba(255, 255, 255, 0.05);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .attribution-line {
        width: 80px;
        height: 1px;
        background: white;
        margin: 0 auto;
        opacity: 0.6;
      }

      .attribution-text {
        font-family: 'Fira Code', monospace;
        font-size: 13px;
        font-weight: 400;
        line-height: 1.5;
        color: white;
        margin: 15px 0;
        letter-spacing: 1px;
        opacity: 0.9;
      }

      /* CTA Section */
      .cta-section {
        padding: 25px 40px;
        text-align: center;
        background: rgba(255, 255, 255, 0.03);
      }

      .cta-text {
        font-family: 'Old Standard TT', serif;
        font-size: 18px;
        font-weight: 700;
        color: white;
        margin-bottom: 5px;
        letter-spacing: 1px;
      }

      .cta-subtext {
        font-family: 'Fira Code', monospace;
        font-size: 12px;
        color: white;
        font-weight: 300;
        letter-spacing: 0.5px;
        opacity: 0.8;
      }

      /* Brand Footer */
      .brand-footer {
        background: rgba(255, 255, 255, 0.08);
        color: white;
        padding: 25px 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      .brand-main {
        text-align: left;
      }

      .brand-name {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 24px;
        letter-spacing: 3px;
        margin-bottom: 3px;
      }

      .brand-location {
        font-family: 'Fira Code', monospace;
        font-size: 11px;
        opacity: 0.8;
        letter-spacing: 2px;
        font-weight: 300;
      }

      .brand-philosophy {
        text-align: right;
      }

      .philosophy-text {
        font-family: 'Fira Code', monospace;
        font-size: 11px;
        opacity: 0.9;
        letter-spacing: 2px;
        margin-bottom: 2px;
        font-weight: 600;
      }

      .philosophy-subtext {
        font-family: 'Fira Code', monospace;
        font-size: 9px;
        opacity: 0.7;
        letter-spacing: 1px;
        font-weight: 300;
      }

      /* Responsive */
      @media (max-width: 450px) {
        .pinterest-pin {
          width: 350px;
          height: 525px;
        }

        .quote-text {
          font-size: 32px;
        }

        .quote-mark {
          font-size: 120px;
        }

        .quote-section {
          padding: 50px 30px;
        }

        .attribution-section,
        .cta-section {
          padding: 20px 30px;
        }

        .brand-footer {
          padding: 20px 30px;
        }
      }

      /* Elegant hover effect */
      .pinterest-pin:hover {
        transform: translateY(-3px);
        transition: transform 0.3s ease;
        box-shadow: 0 40px 80px rgba(0, 0, 0, 0.4);
      }

      /* Subtle animation */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .quote-text {
        animation: fadeIn 1s ease-out;
      }
    </style>
  </body>
</html>
