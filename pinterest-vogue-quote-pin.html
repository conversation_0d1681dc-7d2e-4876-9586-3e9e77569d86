<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pinterest Pin: Vogue Quote - Cathám New York</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="pinterest-pin">
      <!-- Quote Section -->
      <div class="quote-section">
        <div class="quote-mark">"</div>
        <div class="quote-text">
          The most expensive serum in the world<br>
          is worthless if your skin<br>
          <span class="emphasis">can't absorb it.</span>
        </div>
        <div class="quote-mark closing">"</div>
      </div>

      <!-- Editorial Note -->
      <div class="editorial-note">
        <div class="note-line"></div>
        <div class="note-text">
          This is why we start with barrier repair,<br>
          not another peptide promise.
        </div>
        <div class="note-line"></div>
      </div>

      <!-- Sophisticated CTA -->
      <div class="cta-section">
        <div class="cta-text">Discover the European approach</div>
        <div class="cta-subtext">to intelligent skincare</div>
      </div>

      <!-- Brand Footer -->
      <div class="brand-footer">
        <div class="brand-main">
          <div class="brand-name">CATHÁM</div>
          <div class="brand-location">NEW YORK</div>
        </div>
        <div class="brand-philosophy">
          <div class="philosophy-text">EUROPEAN BOTANICALS</div>
          <div class="philosophy-subtext">INTELLIGENT FORMULATION</div>
        </div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Old Standard TT', serif;
        background: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .pinterest-pin {
        width: 400px;
        height: 600px;
        background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
        border-radius: 0;
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
      }

      /* Quote Section */
      .quote-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 60px 40px;
        text-align: center;
        position: relative;
      }

      .quote-mark {
        font-family: 'Old Standard TT', serif;
        font-size: 120px;
        font-weight: 400;
        color: #1a1a1a;
        line-height: 0.8;
        position: absolute;
        opacity: 0.1;
      }

      .quote-mark {
        top: 20px;
        left: 30px;
      }

      .quote-mark.closing {
        bottom: 180px;
        right: 30px;
        transform: rotate(180deg);
      }

      .quote-text {
        font-family: 'Old Standard TT', serif;
        font-size: 32px;
        font-weight: 400;
        line-height: 1.3;
        color: #1a1a1a;
        font-style: italic;
        z-index: 2;
        position: relative;
      }

      .emphasis {
        font-weight: 700;
        font-style: normal;
        position: relative;
      }

      .emphasis::after {
        content: '';
        position: absolute;
        bottom: 2px;
        left: 0;
        right: 0;
        height: 3px;
        background: #1a1a1a;
        opacity: 0.2;
      }

      /* Editorial Note */
      .editorial-note {
        padding: 30px 40px;
        text-align: center;
        background: #fafafa;
        border-top: 1px solid #e8e8e8;
        border-bottom: 1px solid #e8e8e8;
      }

      .note-line {
        width: 60px;
        height: 1px;
        background: #1a1a1a;
        margin: 0 auto;
      }

      .note-text {
        font-family: 'Fira Code', monospace;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.4;
        color: #333;
        margin: 15px 0;
        letter-spacing: 0.3px;
      }

      /* CTA Section */
      .cta-section {
        padding: 25px 40px;
        text-align: center;
        background: #ffffff;
      }

      .cta-text {
        font-family: 'Old Standard TT', serif;
        font-size: 18px;
        font-weight: 700;
        color: #1a1a1a;
        margin-bottom: 5px;
        letter-spacing: 1px;
      }

      .cta-subtext {
        font-family: 'Fira Code', monospace;
        font-size: 12px;
        color: #666;
        font-weight: 300;
        letter-spacing: 0.5px;
      }

      /* Brand Footer */
      .brand-footer {
        background: #1a1a1a;
        color: white;
        padding: 25px 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .brand-main {
        text-align: left;
      }

      .brand-name {
        font-family: 'Old Standard TT', serif;
        font-weight: 700;
        font-size: 24px;
        letter-spacing: 3px;
        margin-bottom: 3px;
      }

      .brand-location {
        font-family: 'Fira Code', monospace;
        font-size: 11px;
        opacity: 0.8;
        letter-spacing: 2px;
        font-weight: 300;
      }

      .brand-philosophy {
        text-align: right;
      }

      .philosophy-text {
        font-family: 'Fira Code', monospace;
        font-size: 10px;
        opacity: 0.9;
        letter-spacing: 1px;
        margin-bottom: 2px;
        font-weight: 500;
      }

      .philosophy-subtext {
        font-family: 'Fira Code', monospace;
        font-size: 9px;
        opacity: 0.7;
        letter-spacing: 0.5px;
        font-weight: 300;
      }

      /* Responsive */
      @media (max-width: 450px) {
        .pinterest-pin {
          width: 350px;
          height: 525px;
        }

        .quote-text {
          font-size: 28px;
        }

        .quote-mark {
          font-size: 100px;
        }

        .quote-section {
          padding: 50px 30px;
        }

        .editorial-note,
        .cta-section {
          padding: 20px 30px;
        }

        .brand-footer {
          padding: 20px 30px;
        }
      }

      /* Elegant hover effect */
      .pinterest-pin:hover {
        transform: translateY(-5px);
        transition: transform 0.3s ease;
        box-shadow: 0 40px 80px rgba(0, 0, 0, 0.2);
      }
    </style>
  </body>
</html>
