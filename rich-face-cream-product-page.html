<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="BARRIER RESTORED. Before Collagen Can Build, Your Barrier Must Hold. This luxuriously rich day cream combines the nourishing power of Cocoa Butter, Argan Oil, and Shea Butter with protective Cloudberry Extract to create an intensive barrier repair treatment. The waxy, velvety texture melts into skin, delivering deep hydration while strengthening your skin's natural defense system. Perfect for dry, dehydrated, and mature skin types. Part of Cathám's Tri-Collagen Lift System Protect & Balance phases. Organic certified, fragrance-free formula."
    />
    <title>Cathám Rich Face Cream - Protect & Balance</title>
  </head>
  <body>
    <div class="magazine-layout">
      <!-- Magazine Header -->
      <header class="magazine-masthead">
        <div class="masthead-text">Cathám | Clean Beauty Revolution</div>
        <h1 class="magazine-title">BARRIER RESTORED</h1>
      </header>

      <!-- Main Content -->
      <div class="magazine-content">
        <!-- Product Feature Section -->
        <section class="product-feature">
          <div class="feature-text-full">
            <p class="feature-lead">Rich Protective Face Cream</p>
            <p class="fira-text">
              This luxuriously rich cream combines the nourishing power of Cocoa
              Butter, Argan Oil, and Shea Butter with protective Cloudberry
              Extract to create an intensive barrier repair treatment. The waxy,
              velvety texture melts into skin, delivering deep hydration while
              strengthening your skin's natural defense system.
              <span class="highlight"
                >Transform dry, compromised skin into a resilient, protected
                complexion that feels soft and comfortable all day.</span
              >
            </p>
          </div>
        </section>

        <!-- Product Details in 90s Magazine Style -->
        <section class="product-details">
          <div class="details-container">
            <!-- System Section -->
            <div class="system-column">
              <h3 class="column-heading fira-heading">
                <strong>TRI-COLLAGENLIFT™ SYSTEM</strong>
              </h3>

              <div data-phase="2" class="phase-container active">
                <div class="phase-header">
                  <h4 class="phase-title">Protect</h4>
                  <div class="phase-number">N°2</div>
                </div>
                <p class="phase-description fira-text">
                  <strong>This is where our Rich Face Cream excels.</strong>
                  Creates a protective barrier that shields skin from
                  environmental stressors while locking in moisture. Cloudberry
                  Extract provides antioxidant protection against free radical
                  damage, while the rich formula prevents trans-epidermal water
                  loss.
                </p>
              </div>

              <div data-phase="3" class="phase-container">
                <div class="phase-header">
                  <h4 class="phase-title">Balance</h4>
                  <div class="phase-number">N°3</div>
                </div>
                <p class="phase-description fira-text">
                  Restores optimal skin barrier function and maintains healthy
                  moisture levels. The blend of organic butters and oils works
                  to rebalance compromised skin, supporting natural repair
                  processes and maintaining long-term skin health.
                </p>
              </div>
            </div>

            <!-- Benefits Column -->
            <div class="benefits-column">
              <h3 class="column-heading fira-heading">
                <strong>KEY BENEFITS</strong>
              </h3>

              <ul class="benefits-list">
                <li class="benefit-item">
                  <div class="benefit-header">
                    <span class="benefit-icon">✦</span>
                    <span class="benefit-title">Intensive Barrier Repair</span>
                  </div>
                  <p class="benefit-description fira-text">
                    Cocoa Butter and Shea Butter work synergistically to restore
                    damaged skin barriers and prevent moisture loss.
                  </p>
                </li>

                <li class="benefit-item">
                  <div class="benefit-header">
                    <span class="benefit-icon">✦</span>
                    <span class="benefit-title"
                      >Deep Nourishment & Comfort</span
                    >
                  </div>
                  <p class="benefit-description fira-text">
                    Argan Oil and Olive Oil deliver essential fatty acids that
                    soften and smooth even the driest skin areas.
                  </p>
                </li>

                <li class="benefit-item">
                  <div class="benefit-header">
                    <span class="benefit-icon">✦</span>
                    <span class="benefit-title">Antioxidant Protection</span>
                  </div>
                  <p class="benefit-description fira-text">
                    Cloudberry Extract provides powerful antioxidants that
                    protect against environmental damage and premature aging.
                  </p>
                </li>

                <li class="benefit-item">
                  <div class="benefit-header">
                    <span class="benefit-icon">✦</span>
                    <span class="benefit-title"
                      >Ideal For Dry & Mature Skin</span
                    >
                  </div>
                  <p class="benefit-description fira-text">
                    Rich, waxy texture perfect for dry skin areas, dehydrated
                    skin, and damaged barriers. Gentle formula with no added
                    fragrance.
                  </p>
                </li>
              </ul>
            </div>
          </div>

          <!-- Application Ritual Section -->
          <div class="ritual-section">
            <h4 class="ritual-heading fira-heading">
              <strong>APPLICATION</strong>
            </h4>
            <p class="ritual-description fira-text">
              Apply to clean skin morning and evening, focusing on dry areas.
              Warm a small amount between fingertips and gently massage into
              face and neck until absorbed. For best results, use as the final
              step in your skincare routine. Suitable for normal, dry, and
              mature skin types. Dermatologically tested. Organic Certified.
            </p>
          </div>
        </section>

        <!-- Ingredients Section -->
        <section class="ingredients-section">
          <h3 class="ingredients-heading fira-heading">
            <strong>FULL INGREDIENTS LIST</strong>
          </h3>
          <p class="ingredients-list fira-text">
            <strong>Key Actives:</strong> Cocoa Butter, Argan Oil, Shea Butter,
            Olive Oil, Cloudberry Extract. <strong>Full INCI:</strong> Aqua,
            Helianthus Annuus (Sunflower) Seed Oil<sup>①</sup>, Theobroma Cacao
            (Cocoa) Seed Butter<sup>①</sup>, Dicaprylyl Carbonate, Pentylene
            Glycol, Polyglyceryl-6 Stearate, Cetearyl Alcohol,
            Glycerin<sup>②</sup>, Argania Spinosa (Argan) Kernel
            Oil<sup>①</sup>, Helianthus Annuus (Sunflower) Seed Cera,
            Butyrospermum Parkii (Shea) Butter<sup>①</sup>, Olea Europaea
            (Olive) Oil Unsaponifiables, Palmitic Acid, Rubus Chamaemorus
            (Cloudberry) Fruit Extract<sup>①</sup>, Stearic Acid, Polyglyceryl-6
            Behenate, Cellulose, Rhus Verniciflua Peel Cera / Rhus Succedanea
            Fruit Cera, Shorea Robusta Resin, Xanthan Gum, Vanilla Planifolia
            (Vanilla) Bean Extract<sup>①</sup>, Sodium Phytate, Ascorbyl
            Palmitate, Tocopherol, Lactic Acid
          </p>
          <p class="ingredients-notes fira-text">
            <sup>①</sup> Ingredients from organic farming <sup>②</sup> Made
            using organic ingredients
          </p>
        </section>
      </div>
    </div>

    <style>
      /* 90s Chic Magazine Style CSS */
      @import url("https://fonts.googleapis.com/css2?family=Bodoni+Moda:wght@400;600;700&display=swap");
      @import url("https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,600;1,400&display=swap");
      @import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500&display=swap");

      /* Fira Code Font */
      @font-face {
        font-family: "Fira Code";
        src: url("fonts/Fira_Code/static/FiraCode-Regular.ttf")
          format("truetype");
        font-weight: 400;
        font-style: normal;
      }

      @font-face {
        font-family: "Fira Code";
        src: url("fonts/Fira_Code/static/FiraCode-Medium.ttf")
          format("truetype");
        font-weight: 500;
        font-style: normal;
      }

      body {
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
      }

      .magazine-layout {
        max-width: 1000px;
        margin: 0 auto;
        font-family: "Cormorant Garamond", serif;
        color: #111;
        line-height: 1.5;
        padding: 20px;
        background-color: #fff;
      }

      /* Fira Code Styling */
      .fira-heading {
        font-family: "Fira Code", monospace;
        letter-spacing: 0.5px;
        font-weight: 500;
        text-transform: uppercase;
      }

      .fira-text {
        font-family: "Fira Code", monospace;
        font-size: 11px !important;
        line-height: 1.7;
        letter-spacing: 0.2px;
      }

      /* Magazine Masthead */
      .magazine-masthead {
        margin-bottom: 30px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 15px;
      }

      .masthead-text {
        font-family: "Montserrat", sans-serif;
        font-size: 12px;
        letter-spacing: 1px;
        color: #666;
        margin-bottom: 10px;
        font-weight: 400;
      }

      .magazine-title {
        font-family: "Bodoni Moda", serif;
        font-size: 3.5rem;
        letter-spacing: 1px;
        line-height: 1;
        margin: 0;
        font-weight: 700;
      }

      /* Feature Text Styling - Full Width */
      .feature-text-full {
        padding-top: 10px;
        max-width: 800px;
        margin: 0 auto;
      }

      .feature-lead {
        font-size: 1.5rem;
        line-height: 1.4;
        margin-bottom: 20px;
        font-weight: 600;
      }

      .product-intro {
        margin: 30px 0;
        border-top: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        padding: 20px 0;
      }

      .product-intro h2 {
        font-family: "Old Standard TT", serif;
        font-size: 2rem;
        margin: 0 0 10px 0;
        letter-spacing: 1px;
      }

      .highlight {
        font-style: italic;
        font-weight: 600;
      }

      /* Product Details Section */
      .product-details {
        margin: 30px 0;
        padding-top: 30px;
        border-top: 1px solid #ddd;
      }

      .details-container {
        display: flex;
        gap: 40px;
        margin-bottom: 40px;
      }

      .system-column,
      .benefits-column {
        flex: 1;
      }

      .column-heading {
        font-family: "Old Standard TT", serif;
        font-size: 1.2rem;
        letter-spacing: 1px;
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #ddd;
      }

      /* Phase Styling */
      .phase-container {
        border: 1px solid #ddd;
        background-color: #fafafa;
        padding: 20px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        margin-bottom: 15px;
        cursor: pointer;
      }

      .phase-container.active {
        border-color: #000;
        background-color: #f0f0f0;
      }

      .phase-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .phase-title {
        font-family: "Cormorant Garamond", serif;
        font-size: 1.2rem;
        margin: 0;
        font-weight: 600;
      }

      .phase-number {
        font-size: 20px;
        font-weight: 700;
        color: #fff;
        background: #000;
        padding: 2px 8px;
        font-family: "Times New Roman", Times, serif;
        letter-spacing: 0.05em;
      }

      .phase-description {
        margin: 0;
      }

      /* Benefits List */
      .benefits-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .benefit-item {
        margin-bottom: 20px;
      }

      .benefit-header {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
      }

      .benefit-icon {
        margin-right: 10px;
        font-size: 1.1rem;
      }

      .benefit-title {
        font-family: "Cormorant Garamond", serif;
        font-size: 1.2rem;
        font-weight: 600;
      }

      .benefit-description {
        margin: 0 0 0 25px;
      }

      /* Ritual Section */
      .ritual-section {
        background: #f5f5f5;
        padding: 25px;
        border-radius: 2px;
      }

      .ritual-heading {
        font-family: "Cormorant Garamond", serif;
        font-size: 1.2rem;
        margin: 0 0 15px 0;
        font-weight: 600;
      }

      .ritual-description {
        margin: 0;
      }

      /* Ingredients Section */
      .ingredients-section {
        margin: 30px 0 20px;
        padding-top: 30px;
        border-top: 1px solid #ddd;
        text-align: left;
      }

      .ingredients-heading {
        font-family: "Bodoni Moda", serif;
        font-size: 1.2rem;
        letter-spacing: 1px;
        margin: 0 0 15px 0;
      }

      .ingredients-list {
        max-width: 800px;
        margin: 0 0 10px;
      }

      .ingredients-notes {
        color: #666;
        margin: 0;
      }

      /* Responsive Adjustments */
      @media (max-width: 768px) {
        .details-container {
          flex-direction: column;
        }

        .magazine-title {
          font-size: 2.5rem;
        }

        .feature-text-full {
          padding: 0 15px;
        }
      }
    </style>

    <script>
      // Add interactivity to the phases
      document.addEventListener("DOMContentLoaded", function () {
        const phases = document.querySelectorAll(".phase-container");

        // Add click event to each phase
        phases.forEach((phase) => {
          phase.addEventListener("click", function () {
            // Toggle active class on clicked phase
            phases.forEach((p) => p.classList.remove("active"));
            this.classList.add("active");

            // Get the phase number for potential additional actions
            const phaseNum = this.getAttribute("data-phase");
            console.log(`Phase ${phaseNum} clicked`);
          });
        });
      });
    </script>
  </body>
</html>
