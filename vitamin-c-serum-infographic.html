<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vitamin C Serum - Ingredient Infographic</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Fira+Code:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="infographic-container">
      <!-- Header -->
      <div class="header-section">
        <div class="product-title">VITAMIN C SERUM</div>
        <div class="subtitle">BRIGHTENING ANTIOXIDANT COMPLEX</div>
        <div class="divider-line"></div>
      </div>

      <!-- Hero Ingredient -->
      <div class="hero-ingredient">
        <div class="ingredient-number">N°1</div>
        <div class="ingredient-content">
          <div class="ingredient-name">2% NATURAL VITAMIN C</div>
          <div class="ingredient-subtitle">Ascorbyl Glucoside</div>
          <div class="ingredient-benefit">
            Stable, gentle vitamin C that brightens skin tone, stimulates
            collagen synthesis, and provides powerful antioxidant protection
            without irritation.
          </div>
        </div>
        <div class="ingredient-icon">🍊</div>
      </div>

      <!-- Supporting Ingredients Grid -->
      <div class="ingredients-grid">
        <div class="ingredient-card">
          <div class="card-number">N°2</div>
          <div class="card-name">1% FERULIC ACID</div>
          <div class="card-subtitle">Vitamin C Booster</div>
          <div class="card-benefit">
            Plant-derived antioxidant that stabilizes and amplifies vitamin C
            effectiveness while providing additional UV protection and
            anti-aging benefits.
          </div>
          <div class="card-icon">🌾</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°3</div>
          <div class="card-name">1% SEA BUCKTHORN OIL</div>
          <div class="card-subtitle">Vitamin C Powerhouse</div>
          <div class="card-benefit">
            Arctic superfruit oil containing 15x more vitamin C than oranges.
            Accelerates skin renewal and provides intense antioxidant
            protection.
          </div>
          <div class="card-icon">🧡</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°4</div>
          <div class="card-name">HYALURONIC ACID DUO</div>
          <div class="card-subtitle">Multi-Weight Hydration</div>
          <div class="card-benefit">
            Hydrolyzed hyaluronic acid and sodium hyaluronate provide instant
            plumping, deep hydration, and long-lasting moisture retention for
            glowing skin.
          </div>
          <div class="card-icon">💧</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°5</div>
          <div class="card-name">NOURISHING OIL BLEND</div>
          <div class="card-subtitle">Vitamin E + Botanical Oils</div>
          <div class="card-benefit">
            Jojoba, almond, avocado, and rosehip oils with vitamin E create a
            protective barrier while delivering nutrients deep into skin.
          </div>
          <div class="card-icon">✨</div>
        </div>

        <div class="ingredient-card">
          <div class="card-number">N°6</div>
          <div class="card-name">SODIUM PCA</div>
          <div class="card-subtitle">Natural Moisturizing Factor</div>
          <div class="card-benefit">
            Skin-identical humectant that attracts and binds moisture, ensuring
            vitamin C penetrates effectively while maintaining hydration
            balance.
          </div>
          <div class="card-icon">🌊</div>
        </div>
      </div>

      <!-- Key Benefits Summary -->
      <div class="benefits-section">
        <div class="benefits-title">BRIGHTENING BENEFITS</div>
        <div class="benefits-grid">
          <div class="benefit-item">
            <div class="benefit-icon">✨</div>
            <div class="benefit-text">Brightens skin tone</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">🛡️</div>
            <div class="benefit-text">Antioxidant protection</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">⚡</div>
            <div class="benefit-text">Stimulates collagen</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">🌅</div>
            <div class="benefit-text">Morning glow ritual</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">🌿</div>
            <div class="benefit-text">Gentle, stable formula</div>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">💎</div>
            <div class="benefit-text">Luxurious serum texture</div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer-section">
        <div class="brand-name">CATHÁM</div>
        <div class="brand-tagline">Where Science Meets Skin Intelligence</div>
      </div>
    </div>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Fira Code", monospace;
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        padding: 40px 20px;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .infographic-container {
        max-width: 900px;
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 25px 70px rgba(255, 152, 0, 0.15);
        overflow: hidden;
        position: relative;
      }

      /* Header */
      .header-section {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        color: #ffffff;
        padding: 50px 40px;
        text-align: center;
      }

      .product-title {
        font-family: "Old Standard TT", serif;
        font-size: 32px;
        font-weight: 700;
        letter-spacing: 3px;
        margin-bottom: 15px;
      }

      .subtitle {
        font-size: 14px;
        letter-spacing: 2px;
        opacity: 0.9;
        margin-bottom: 30px;
      }

      .divider-line {
        width: 60px;
        height: 2px;
        background: #ffffff;
        margin: 0 auto;
      }

      /* Hero Ingredient */
      .hero-ingredient {
        padding: 50px 40px;
        background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
        display: flex;
        align-items: center;
        gap: 30px;
        border-bottom: 1px solid #ffcc02;
      }

      .ingredient-number {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%);
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Old Standard TT", serif;
        font-size: 24px;
        font-weight: 700;
        flex-shrink: 0;
      }

      .ingredient-content {
        flex: 1;
      }

      .ingredient-name {
        font-family: "Old Standard TT", serif;
        font-size: 28px;
        font-weight: 700;
        color: #f57c00;
        margin-bottom: 8px;
        letter-spacing: 1px;
      }

      .ingredient-subtitle {
        font-size: 14px;
        color: #ff9800;
        font-weight: 600;
        margin-bottom: 15px;
        letter-spacing: 1px;
      }

      .ingredient-benefit {
        font-size: 16px;
        line-height: 1.6;
        color: #333333;
      }

      .ingredient-icon {
        font-size: 40px;
        flex-shrink: 0;
      }

      /* Ingredients Grid */
      .ingredients-grid {
        padding: 50px 40px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
      }

      .ingredient-card {
        background: #ffffff;
        border: 1px solid #ffcc02;
        border-radius: 15px;
        padding: 25px;
        position: relative;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .ingredient-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(255, 152, 0, 0.2);
      }

      .card-number {
        position: absolute;
        top: -15px;
        left: 20px;
        width: 30px;
        height: 30px;
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 700;
      }

      .card-name {
        font-family: "Old Standard TT", serif;
        font-size: 16px;
        font-weight: 700;
        color: #f57c00;
        margin-bottom: 8px;
        letter-spacing: 0.5px;
      }

      .card-subtitle {
        font-size: 11px;
        color: #ff9800;
        font-weight: 600;
        margin-bottom: 12px;
        letter-spacing: 0.5px;
      }

      .card-benefit {
        font-size: 12px;
        line-height: 1.5;
        color: #333333;
        margin-bottom: 12px;
      }

      .card-icon {
        font-size: 20px;
        text-align: right;
      }

      /* Benefits Section */
      .benefits-section {
        background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
        padding: 50px 40px;
      }

      .benefits-title {
        font-family: "Old Standard TT", serif;
        font-size: 24px;
        font-weight: 700;
        text-align: center;
        margin-bottom: 30px;
        letter-spacing: 2px;
        color: #f57c00;
      }

      .benefits-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
      }

      .benefit-item {
        display: flex;
        align-items: center;
        gap: 12px;
        background: #ffffff;
        padding: 18px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(255, 152, 0, 0.1);
      }

      .benefit-icon {
        font-size: 18px;
        flex-shrink: 0;
      }

      .benefit-text {
        font-size: 13px;
        font-weight: 600;
        color: #333333;
      }

      /* Footer */
      .footer-section {
        background: #f57c00;
        color: #ffffff;
        padding: 40px;
        text-align: center;
      }

      .brand-name {
        font-family: "Old Standard TT", serif;
        font-size: 28px;
        font-weight: 700;
        letter-spacing: 3px;
        margin-bottom: 10px;
      }

      .brand-tagline {
        font-size: 12px;
        letter-spacing: 1px;
        opacity: 0.8;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .infographic-container {
          margin: 20px;
        }

        .header-section,
        .hero-ingredient,
        .ingredients-grid,
        .benefits-section,
        .footer-section {
          padding: 30px 20px;
        }

        .product-title {
          font-size: 24px;
        }

        .hero-ingredient {
          flex-direction: column;
          text-align: center;
        }

        .ingredients-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .benefits-grid {
          grid-template-columns: repeat(2, 1fr);
        }

        .ingredient-name {
          font-size: 24px;
        }

        .card-name {
          font-size: 14px;
        }
      }

      @media (max-width: 480px) {
        .benefits-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </body>
</html>
